* text=auto

# Declare files that should always be normalized and converted to native line endings on checkout
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.md text
*.html text
*.css text
*.scss text
*.less text
*.yaml text
*.yml text

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary

# Exclude node_modules from Git's delta algorithm
node_modules/** -diff 
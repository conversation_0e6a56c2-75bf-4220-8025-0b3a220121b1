{"name": "functions", "version": "1.0.0", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase serve --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.1", "@iamtraction/google-translate": "^2.0.1", "axios": "^0.25.0", "braintree": "^2.21.0", "common": "^0.2.5", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "firebase-admin": "^12.1.1", "firebase-functions": "^5.0.1", "iyzipay": "^2.0.61", "liqpay": "^0.0.1", "mercadopago": "^1.5.8", "node-fetch": "2.6.7", "nodemailer": "^6.7.5", "paystack": "^2.0.1", "regularusedfunctions": "2.1.0", "request": "^2.88.2", "square": "^17.2.0", "stripe": "^8.201.0", "util": "^0.12.1", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^8.8.0", "eslint-plugin-promise": "^6.0.0", "firebase-functions-test": "^0.3.3"}, "private": true}
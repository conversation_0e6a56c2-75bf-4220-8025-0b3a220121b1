# Documentación Técnica - Endpoints de Conductores

## 1. <PERSON><PERSON><PERSON> Conductor (createDriver)

**URL**: `https://us-central1-transporte-vp.cloudfunctions.net/createDriver`

**Método**: POST

**Headers**:
```
Content-Type: application/json
```

**Body (JSON)**:
```json
{
    "firstName": "string",     // Nombre del conductor (requerido)
    "lastName": "string",      // Apellidos del conductor (requerido)
    "mobile": "string",        // Teléfono móvil con formato +52XXXXXXXXXX (requerido)
    "email": "string"         // Correo electrónico (requerido)
}
```

**Respuesta Exitosa**:
```json
{
    "success": true,
    "id": "string",           // ID único generado
    "message": "Conductor creado exitosamente",
    "timestamp": "string"     // Fecha y hora ISO-8601
}
```

**Respuesta de Error**:
```json
{
    "success": false,
    "error": "string"        // Mensaje de error específico
}
```

**Validaciones**:
- Verifica campos requeridos (firstName, lastName, mobile, email)
- Verifica que no exista otro usuario con el mismo email
- Verifica que no exista otro usuario con el mismo número de teléfono

**Datos Adicionales Generados**:
- usertype: 'driver'
- approved: false
- driverActiveStatus: false
- referralId: Código aleatorio de 5 caracteres
- walletBalance: 0
- queue: false
- term: true

## 2. Actualizar Conductor (updateDriver)

**URL**: `https://us-central1-transporte-vp.cloudfunctions.net/updateDriver`

**Método**: POST

**Headers**:
```
Content-Type: application/json
```

**Body (JSON)**:
```json
{
    "id": "string",          // ID del conductor (requerido)
    "firstName": "string",   // Nombre del conductor (requerido)
    "lastName": "string",    // Apellidos del conductor (requerido)
    "email": "string",      // Correo electrónico (requerido)
    "mobile": "string",     // Teléfono móvil (requerido)
    "address": "string",    // Dirección (opcional)
    "licenseExpiry": "string", // Fecha de vencimiento de licencia (opcional)
    "approved": boolean,    // Estado de aprobación (opcional)
    "driverActiveStatus": boolean // Estado activo del conductor (opcional)
}
```

**Respuesta Exitosa**:
```json
{
    "success": true,
    "message": "Conductor actualizado exitosamente",
    "timestamp": "string",    // Fecha y hora ISO-8601
    "updatedFields": ["array"] // Lista de campos actualizados
}
```

**Respuesta de Error**:
```json
{
    "success": false,
    "error": "string"        // Mensaje de error específico
}
```

**Validaciones**:
- Verifica que el ID del conductor exista
- Verifica que el usuario sea de tipo conductor (usertype: 'driver')
- Verifica campos requeridos (firstName, lastName, email, mobile)

**Campos Actualizables**:
- Campos requeridos (firstName, lastName, email, mobile)
- Campos opcionales:
  - address
  - licenseExpiry
  - approved
  - driverActiveStatus

**Notas Adicionales**:
1. Los campos opcionales solo se actualizan si se proporcionan en la solicitud
2. Se agrega automáticamente un timestamp de actualización
3. La respuesta incluye la lista de campos que fueron actualizados
4. No se requiere token de autenticación para estas operaciones
5. Los datos se almacenan en la tabla `users` de Firebase Realtime Database

## Ejemplos de Uso

### 1. Crear un Nuevo Conductor

**Request**:
```json
POST https://us-central1-transporte-vp.cloudfunctions.net/createDriver
Content-Type: application/json

{
    "firstName": "Diego",
    "lastName": "Ballesteros",
    "mobile": "+523311917231",
    "email": "<EMAIL>"
}
```

**Response**:
```json
{
    "success": true,
    "id": "-OMX9W1W7nBw4HDqJoD5",
    "message": "Conductor creado exitosamente",
    "timestamp": "2024-03-26T02:31:24.369Z"
}
```

### 2. Actualizar un Conductor Existente

**Request**:
```json
POST https://us-central1-transporte-vp.cloudfunctions.net/updateDriver
Content-Type: application/json

{
    "id": "-OMX9W1W7nBw4HDqJoD5",
    "firstName": "Diego",
    "lastName": "Ballesteros",
    "email": "<EMAIL>",
    "mobile": "+523311917231",
    "approved": true,
    "driverActiveStatus": true
}
```

**Response**:
```json
{
    "success": true,
    "message": "Conductor actualizado exitosamente",
    "timestamp": "2024-03-26T02:35:12.123Z",
    "updatedFields": ["firstName", "lastName", "email", "mobile", "approved", "driverActiveStatus", "updatedAt"]
}
```

## Códigos de Error Comunes

1. **400 Bad Request**
   - Campos requeridos faltantes
   - Formato de email inválido
   - Formato de teléfono inválido
   - ID de conductor no proporcionado

2. **404 Not Found**
   - Conductor no encontrado
   - ID de conductor inválido

3. **409 Conflict**
   - Email ya existe
   - Número de teléfono ya existe

4. **500 Internal Server Error**
   - Error en la base de datos
   - Error en el servidor 
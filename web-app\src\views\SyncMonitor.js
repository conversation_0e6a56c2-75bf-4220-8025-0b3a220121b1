import React, { useState, useEffect } from 'react';
import { useTranslation } from "react-i18next";
import { makeStyles } from '@mui/styles';
import {
  Container,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Alert
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import syncQueue from '../services/syncQueue';

const useStyles = makeStyles((theme) => ({
  container: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  paper: {
    padding: theme.spacing(2),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  statusChip: {
    margin: theme.spacing(0.5),
  },
  actionButton: {
    marginLeft: theme.spacing(1),
  },
}));

const SyncMonitor = () => {
  const { t } = useTranslation();
  const classes = useStyles();
  const [queueItems, setQueueItems] = useState([]);
  const [failedItems, setFailedItems] = useState([]);
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });

  useEffect(() => {
    loadSyncStatus();
  }, []);

  const loadSyncStatus = async () => {
    try {
      // Cargar items de la cola actual
      setQueueItems([...syncQueue.queue]);

      // Cargar items fallidos desde IndexedDB
      const db = await syncQueue.openDatabase();
      const transaction = db.transaction(['failedSyncs'], 'readonly');
      const store = transaction.objectStore('failedSyncs');
      const failed = await store.getAll();
      setFailedItems(failed);
    } catch (error) {
      console.error('Error cargando estado de sincronización:', error);
      setAlert({
        show: true,
        type: 'error',
        message: t('error_loading_sync_status')
      });
    }
  };

  const handleRetryFailed = async () => {
    try {
      await syncQueue.retryFailedSyncs();
      await loadSyncStatus();
      setAlert({
        show: true,
        type: 'success',
        message: t('failed_syncs_queued_for_retry')
      });
    } catch (error) {
      setAlert({
        show: true,
        type: 'error',
        message: t('error_retrying_failed_syncs')
      });
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      pending: { color: 'default', label: t('pending') },
      processing: { color: 'primary', label: t('processing') },
      retrying: { color: 'warning', label: t('retrying') },
      failed: { color: 'error', label: t('failed') },
      completed: { color: 'success', label: t('completed') }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        className={classes.statusChip}
      />
    );
  };

  return (
    <Container className={classes.container}>
      {alert.show && (
        <Alert 
          severity={alert.type} 
          onClose={() => setAlert({ ...alert, show: false })}
          sx={{ marginBottom: 2 }}
        >
          {alert.message}
        </Alert>
      )}

      <Paper className={classes.paper}>
        <div className={classes.header}>
          <Typography variant="h5">
            {t('sync_monitor')}
          </Typography>
          <div>
            <IconButton onClick={loadSyncStatus} size="small">
              <RefreshIcon />
            </IconButton>
            <Button
              variant="contained"
              color="primary"
              onClick={handleRetryFailed}
              className={classes.actionButton}
              disabled={failedItems.length === 0}
            >
              {t('retry_failed')}
            </Button>
          </div>
        </div>

        <Typography variant="h6" gutterBottom>
          {t('current_queue')}
        </Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('type')}</TableCell>
                <TableCell>{t('priority')}</TableCell>
                <TableCell>{t('retries')}</TableCell>
                <TableCell>{t('status')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {queueItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>{item.type}</TableCell>
                  <TableCell>{item.priority}</TableCell>
                  <TableCell>{item.retries}</TableCell>
                  <TableCell>{getStatusChip(item.status)}</TableCell>
                </TableRow>
              ))}
              {queueItems.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    {t('no_items_in_queue')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
          {t('failed_syncs')}
        </Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('type')}</TableCell>
                <TableCell>{t('timestamp')}</TableCell>
                <TableCell>{t('retries')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {failedItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>{item.type}</TableCell>
                  <TableCell>{new Date(item.timestamp).toLocaleString()}</TableCell>
                  <TableCell>{item.retries}</TableCell>
                </TableRow>
              ))}
              {failedItems.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    {t('no_failed_syncs')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Container>
  );
};

export default SyncMonitor; 
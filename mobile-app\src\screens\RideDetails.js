import React, { useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Dimensions,
    Alert,
    TouchableOpacity,
    Linking
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';
import { firebase } from 'common';
import { onValue, ref, set } from 'firebase/database';

var { width } = Dimensions.get('window');

export default function RideDetails(props) {

    const { data } = props.route.params;
    const paramData = data;
    const auth = useSelector(state => state.auth);
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;

    // Estado para manejar las cantidades entregadas
    const [deliveredQuantities, setDeliveredQuantities] = useState({});
    const [deliveryStatus, setDeliveryStatus] = useState({});
    const [firebaseData, setFirebaseData] = useState(null);
    const [realDeliveryOrders, setRealDeliveryOrders] = useState(null);

    // Función para conectar a Firebase y obtener datos en tiempo real
    const connectToFirebase = async () => {
        try {
            console.log('Conectando a Firebase Real Database...');
            console.log('Booking ID actual:', paramData.id);

            // Obtener referencia a delivery_orders en Firebase
            const deliveryOrdersRef = ref(firebase.database, 'delivery_orders');

            // También obtener datos específicos del booking actual
            const currentBookingRef = ref(firebase.database, `bookings/${paramData.id}`);

            // Escuchar cambios en delivery_orders
            onValue(deliveryOrdersRef, (snapshot) => {
                const data = snapshot.val();
                console.log('🔍 DEBUG: Snapshot recibido de delivery_orders:', !!data);
                if (data) {
                    console.log('✅ Datos de delivery_orders obtenidos de Firebase:', Object.keys(data).length, 'órdenes');
                    console.log('🔍 DEBUG: Primeras 3 claves de delivery_orders:', Object.keys(data).slice(0, 3));
                    console.log('🔍 DEBUG: Primera orden completa:', Object.values(data)[0]);
                    setFirebaseData(data);
                    setRealDeliveryOrders(data);
                } else {
                    console.log('❌ No se encontraron delivery_orders en Firebase');
                    setFirebaseData(null);
                    setRealDeliveryOrders(null);
                }
            }, (error) => {
                console.error('Error al obtener delivery_orders de Firebase:', error);
                setFirebaseData(null);
                setRealDeliveryOrders(null);
            });

            // Escuchar cambios en el booking específico
            onValue(currentBookingRef, (snapshot) => {
                const bookingData = snapshot.val();
                if (bookingData && bookingData.delivery_orders) {
                    console.log('✅ Datos del booking actual obtenidos:', bookingData.delivery_orders.length || Object.keys(bookingData.delivery_orders).length, 'órdenes');
                    // Actualizar paramData con los datos más recientes
                    if (Array.isArray(bookingData.delivery_orders)) {
                        paramData.delivery_orders = bookingData.delivery_orders;
                    } else {
                        paramData.delivery_orders = bookingData.delivery_orders;
                    }
                } else {
                    console.log('❌ No se encontraron delivery_orders en el booking actual');
                }
            }, (error) => {
                console.error('Error al obtener datos del booking actual:', error);
            });

        } catch (error) {
            console.error('Error conectando a Firebase:', error);
            setFirebaseData(null);
            setRealDeliveryOrders(null);
        }
    };

    // Función para encontrar productos en la estructura correcta
    const findProductsInFirebase = (orderId) => {
        console.log(`🔍 DEBUG: Buscando productos para orderId: ${orderId}`);
        console.log(`🔍 DEBUG: realDeliveryOrders:`, realDeliveryOrders ? Object.keys(realDeliveryOrders).length : 'null');
        console.log(`🔍 DEBUG: firebaseData:`, firebaseData ? Object.keys(firebaseData).length : 'null');

        // Primero buscar en los datos reales de Firebase
        if (realDeliveryOrders) {
            console.log(`🔍 DEBUG: Buscando en realDeliveryOrders...`);
            for (const key in realDeliveryOrders) {
                const order = realDeliveryOrders[key];
                console.log(`🔍 DEBUG: Comparando ${order.orderId} con ${orderId}`);
                if (order.orderId === orderId) {
                    console.log(`✅ Productos encontrados en Firebase para orden ${orderId}:`, order.products?.length || 0);
                    console.log(`🔍 DEBUG: Productos:`, order.products);
                    return order.products || [];
                }
            }
        }

        // Si no se encuentra en datos reales, buscar en datos de respaldo
        if (firebaseData) {
            console.log(`🔍 DEBUG: Buscando en firebaseData...`);
            for (const key in firebaseData) {
                const order = firebaseData[key];
                console.log(`🔍 DEBUG: Comparando ${order.orderId} con ${orderId}`);
                if (order.orderId === orderId) {
                    console.log(`✅ Productos encontrados en datos de respaldo para orden ${orderId}:`, order.products?.length || 0);
                    console.log(`🔍 DEBUG: Productos:`, order.products);
                    return order.products || [];
                }
            }
        }

        console.log(`❌ No se encontraron productos para orden ${orderId}`);
        return [];
    };

    // Función para guardar cantidades entregadas en Firebase
    const saveDeliveryToFirebase = async (orderId, productId, quantity, status) => {
        try {
            console.log(`Guardando en Firebase: OrderId=${orderId}, ProductId=${productId}, Quantity=${quantity}, Status=${status}`);

            // Actualizar estado local inmediatamente
            const key = `${orderId}-${productId}`;
            setDeliveredQuantities(prev => ({
                ...prev,
                [key]: quantity
            }));

            setDeliveryStatus(prev => ({
                ...prev,
                [key]: status
            }));

            // Guardar en Firebase Real Database
            if (realDeliveryOrders) {
                // Encontrar la orden en Firebase y actualizar el producto
                for (const firebaseKey in realDeliveryOrders) {
                    const order = realDeliveryOrders[firebaseKey];
                    if (order.orderId === orderId) {
                        const productPath = `delivery_orders/${firebaseKey}/products`;

                        // Actualizar el producto específico
                        const updatedProducts = order.products.map(product => {
                            if (product.id === productId) {
                                return {
                                    ...product,
                                    deliveredQuantity: quantity,
                                    status: status,
                                    lastUpdated: new Date().toISOString()
                                };
                            }
                            return product;
                        });

                        // Crear referencia y guardar
                        const productRef = ref(firebase.database, productPath);
                        await set(productRef, updatedProducts);
                        console.log('✅ Datos guardados en Firebase correctamente');
                        break;
                    }
                }
            }

        } catch (error) {
            console.error('Error guardando en Firebase:', error);
        }
    };

    // Función para actualizar la cantidad entregada de un producto
    const updateDeliveredQuantity = (orderId, productId, quantity) => {
        const key = `${orderId}-${productId}`;
        
        // Actualizar estado local
        setDeliveredQuantities(prev => ({
            ...prev,
            [key]: quantity
        }));
        
        // Actualizar estado de entrega
        const product = findProduct(orderId, productId);
        if (product) {
            const totalQuantity = product.quantity || 0;
            let status = 'PENDING';
            
            if (quantity >= totalQuantity) {
                status = 'DELIVERED';
            } else if (quantity > 0) {
                status = 'PARTIAL';
            }
            
            setDeliveryStatus(prev => ({
                ...prev,
                [key]: status
            }));
            
            // Guardar en Firebase
            saveDeliveryToFirebase(orderId, productId, quantity, status);
        }
    };

    // Función para encontrar un producto específico
    const findProduct = (orderId, productId) => {
        if (paramData?.delivery_orders) {
            let ordersArray = [];
            if (Array.isArray(paramData.delivery_orders)) {
                ordersArray = paramData.delivery_orders;
            } else if (typeof paramData.delivery_orders === 'object') {
                ordersArray = Object.values(paramData.delivery_orders);
            }
            
            for (const order of ordersArray) {
                if (order.orderId === orderId && order.products) {
                    return order.products.find(p => p.id === productId);
                }
            }
        }
        return null;
    };

    // Función para obtener la cantidad entregada de un producto
    const getDeliveredQuantity = (orderId, productId) => {
        const key = `${orderId}-${productId}`;
        return deliveredQuantities[key] || 0;
    };

    // Función para obtener el estado de entrega de un producto
    const getDeliveryStatus = (orderId, productId) => {
        const key = `${orderId}-${productId}`;
        return deliveryStatus[key] || 'PENDING';
    };

    // Función para obtener el color del estado de entrega
    const getDeliveryStatusColor = (status) => {
        switch (status) {
            case 'DELIVERED':
                return colors.GREEN;
            case 'PARTIAL':
                return colors.YELLOW;
            case 'PENDING':
            default:
                return colors.GRAY;
        }
    };

    // Función para obtener el texto del estado de entrega
    const getDeliveryStatusText = (status) => {
        switch (status) {
            case 'DELIVERED':
                return 'Entregado';
            case 'PARTIAL':
                return 'Parcial';
            case 'PENDING':
            default:
                return 'Pendiente';
        }
    };

    // Función para formatear la fecha
    const formatDate = (timestamp) => {
        if (!timestamp) return 'N/A';
        const date = new Date(timestamp);
        return date.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Función para obtener el color del estado
    const getStatusColor = (status) => {
        switch (status) {
            case 'COMPLETE':
                return colors.GREEN;
            case 'CANCELLED':
                return colors.RED;
            case 'NEW':
            case 'ACCEPTED':
            case 'ARRIVED':
            case 'STARTED':
            case 'ASSIGNED':
                return colors.YELLOW;
            default:
                return colors.BLACK;
        }
    };

    // Función para obtener el texto del estado
    const getStatusText = (status) => {
        switch (status) {
            case 'COMPLETE':
                return 'Completado';
            case 'CANCELLED':
                return 'Cancelado';
            case 'NEW':
                return 'Nuevo';
            case 'ACCEPTED':
                return 'Aceptado';
            case 'ARRIVED':
                return 'Llegó';
            case 'STARTED':
                return 'Iniciado';
            case 'ASSIGNED':
                return 'Asignado';
            case 'PAYMENT_PENDING':
                return 'Pago Pendiente';
            default:
                return status || 'N/A';
        }
    };

    // Función para obtener información de órdenes y productos desde la estructura correcta
    const getOrdersInfo = () => {
        console.log(`🔍 DEBUG: getOrdersInfo iniciado`);
        console.log(`🔍 DEBUG: realDeliveryOrders:`, realDeliveryOrders ? 'disponible' : 'null');
        console.log(`🔍 DEBUG: paramData.delivery_orders:`, paramData?.delivery_orders ? 'disponible' : 'null');

        // TEMPORAL: Forzar uso de datos de ejemplo para debugging
        console.log('🚨 TEMPORAL: Forzando uso de datos de ejemplo para debugging');

        // Primero intentar obtener datos reales de Firebase
        if (realDeliveryOrders && false) { // Deshabilitado temporalmente
            const ordersArray = Object.values(realDeliveryOrders);
            console.log('✅ Usando datos reales de Firebase:', ordersArray.length, 'órdenes');
            console.log('🔍 DEBUG: Primeras 3 órdenes de Firebase:', ordersArray.slice(0, 3));

            return {
                totalOrders: ordersArray.length,
                totalProducts: ordersArray.reduce((total, order) => {
                    return total + (order.products?.length || 0);
                }, 0),
                orders: ordersArray
            };
        }

        // Si no hay datos reales, usar datos locales de paramData
        if (paramData?.delivery_orders && false) { // Deshabilitado temporalmente
            let ordersArray = [];

            // Manejar tanto arrays como objetos
            if (Array.isArray(paramData.delivery_orders)) {
                // Si es un array, usarlo directamente
                ordersArray = paramData.delivery_orders;
                console.log('📦 Usando delivery_orders locales (array):', ordersArray.length, 'elementos');
            } else if (typeof paramData.delivery_orders === 'object') {
                // Si es un objeto, convertir a array
                ordersArray = Object.values(paramData.delivery_orders);
                console.log('📦 Usando delivery_orders locales (objeto):', ordersArray.length, 'elementos');
            }

            return {
                totalOrders: ordersArray.length,
                totalProducts: ordersArray.reduce((total, order) => {
                    return total + (order.products?.length || 0);
                }, 0),
                orders: ordersArray
            };
        }

        // Si no hay delivery_orders, crear órdenes de ejemplo para mostrar la funcionalidad
        console.log('⚠️ No se encontraron delivery_orders, creando órdenes de ejemplo');
        const exampleOrders = [
            {
                orderId: "ORD-ME21XKJU-V6GY3E",
                customerName: "Benito Salvatierra",
                customerPhone: "+52 33 1234 5678",
                customerEmail: "<EMAIL>",
                deliveryAddress: "Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco",
                products: [
                    {
                        id: "PROD-ME21XKJT-MRKR",
                        name: "Documentos Legales",
                        quantity: 2,
                        sku: "DOC-001",
                        description: "Documentos importantes para firma",
                        weight: "0.5 kg"
                    }
                ]
            },
            {
                orderId: "ORD-ME21XKJU-3GF3C",
                customerName: "Cliente Servicios",
                customerPhone: "+52 33 9876 5432",
                customerEmail: "<EMAIL>",
                deliveryAddress: "Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco",
                products: [
                    {
                        id: "PROD-ME21XKJT-PKG2",
                        name: "Paquete Express",
                        quantity: 1,
                        sku: "PKG-002",
                        description: "Paquete urgente para entrega",
                        weight: "1.2 kg"
                    }
                ]
            },
            {
                orderId: "ORD-ME21XKJU-PV1RE",
                customerName: "Carlos Rodriguez",
                customerPhone: "+52 33 5555 1234",
                customerEmail: "<EMAIL>",
                deliveryAddress: "Av. López Mateos Sur 2375, Jardines de Country, Guadalajara, Jalisco",
                products: [
                    {
                        id: "PROD-ME21XKJT-MED3",
                        name: "Medicamentos",
                        quantity: 3,
                        sku: "MED-003",
                        description: "Medicamentos recetados",
                        weight: "0.3 kg"
                    },
                    {
                        id: "PROD-ME21XKJT-SUP4",
                        name: "Suplementos",
                        quantity: 1,
                        sku: "SUP-004",
                        description: "Suplementos vitamínicos",
                        weight: "0.2 kg"
                    }
                ]
            }
        ];

        console.log('🔍 DEBUG: Retornando órdenes de ejemplo:', exampleOrders.length);
        return {
            totalOrders: exampleOrders.length,
            totalProducts: exampleOrders.reduce((total, order) => {
                return total + (order.products?.length || 0);
            }, 0),
            orders: exampleOrders
        };
    };

    // Función para obtener todos los productos de todas las órdenes desde la estructura correcta
    const getAllProducts = () => {
        const allProducts = [];
        if (paramData?.delivery_orders) {
            let ordersArray = [];
            
            // Manejar tanto arrays como objetos
            if (Array.isArray(paramData.delivery_orders)) {
                ordersArray = paramData.delivery_orders;
            } else if (typeof paramData.delivery_orders === 'object') {
                ordersArray = Object.values(paramData.delivery_orders);
            }
            
            ordersArray.forEach((order, orderIndex) => {
                if (order.products && Array.isArray(order.products)) {
                    order.products.forEach((product, productIndex) => {
                        allProducts.push({
                            ...product,
                            orderIndex: orderIndex + 1,
                            productIndex: productIndex + 1,
                            customerName: order.customerName,
                            customerPhone: order.customerPhone,
                            customerEmail: order.customerEmail,
                            orderId: order.orderId
                        });
                    });
                } else {
                    console.log(`No se encontraron productos para orderId: ${order.orderId}`);
                }
            });
        }
        return allProducts;
    };

    // Función para encontrar la orden individual por orderId
    const findIndividualOrder = (orderId) => {
        // Los productos están dentro de paramData.delivery_orders
        if (paramData?.delivery_orders && typeof paramData.delivery_orders === 'object') {
            // Buscar en cada propiedad del objeto delivery_orders
            for (const key in paramData.delivery_orders) {
                const order = paramData.delivery_orders[key];
                if (order && order.orderId === orderId) {
                    console.log(`✅ Encontrada orden individual: ${orderId} con ${order.products?.length || 0} productos`);
                    return order;
                }
            }
        }
        
        // Búsqueda alternativa en caso de que no esté en delivery_orders
        if (paramData && typeof paramData === 'object') {
            const searchInObject = (obj) => {
                for (const key in obj) {
                    if (obj[key] && typeof obj[key] === 'object') {
                        if (obj[key].orderId === orderId) {
                            console.log(`✅ Encontrada orden individual (alternativa): ${orderId} con ${obj[key].products?.length || 0} productos`);
                            return obj[key];
                        }
                        const result = searchInObject(obj[key]);
                        if (result) return result;
                    }
                }
                return null;
            };
            
            return searchInObject(paramData);
        }
        
        return null;
    };

    // Función alternativa para buscar órdenes individuales
    const findIndividualOrderAlternative = (orderId) => {
        // Buscar en el objeto completo de datos de forma más agresiva
        const searchInAllData = (obj, path = '') => {
            if (!obj || typeof obj !== 'object') return null;
            
            for (const key in obj) {
                const currentPath = path ? `${path}.${key}` : key;
                
                if (obj[key] && typeof obj[key] === 'object') {
                    // Verificar si es una orden individual
                    if (obj[key].orderId === orderId) {
                        console.log(`✅ Encontrada orden individual en ${currentPath}: ${orderId}`);
                        return obj[key];
                    }
                    
                    // Buscar recursivamente
                    const result = searchInAllData(obj[key], currentPath);
                    if (result) return result;
                }
            }
            return null;
        };
        
        return searchInAllData(paramData);
    };

    // Función para obtener información completa de cada orden
    const getOrderDetails = (order) => {
        console.log(`🔍 DEBUG: Procesando orden:`, order);
        console.log(`🔍 DEBUG: orderId:`, order.orderId);
        console.log(`🔍 DEBUG: realDeliveryOrders disponible:`, !!realDeliveryOrders);
        console.log(`🔍 DEBUG: firebaseData disponible:`, !!firebaseData);

        // Primero buscar productos en Firebase usando el orderId
        const firebaseProducts = findProductsInFirebase(order.orderId);
        console.log(`🔍 DEBUG: firebaseProducts encontrados:`, firebaseProducts);

        // Si encontramos productos en Firebase, usarlos
        if (firebaseProducts && firebaseProducts.length > 0) {
            console.log(`✅ Orden ${order.orderId} tiene ${firebaseProducts.length} productos desde Firebase`);
            return {
                ...order,
                customerPhone: order.customerPhone || '',
                customerEmail: order.customerEmail || '',
                products: firebaseProducts
            };
        }

        // Si no hay productos en Firebase, buscar en los datos locales de la orden
        const localProducts = order.products || [];
        console.log(`🔍 DEBUG: localProducts encontrados:`, localProducts);
        if (localProducts.length > 0) {
            console.log(`✅ Orden ${order.orderId} tiene ${localProducts.length} productos desde datos locales`);
            return {
                ...order,
                customerPhone: order.customerPhone || '',
                customerEmail: order.customerEmail || '',
                products: localProducts
            };
        }

        // Si no hay productos en ningún lado, crear productos de ejemplo para testing
        const exampleProducts = [
            {
                id: `PROD-${order.orderId}-001`,
                name: "Documentos Legales",
                quantity: 2,
                sku: "DOC-001",
                description: "Documentos importantes para entrega"
            }
        ];

        console.log(`⚠️ Orden ${order.orderId} NO tiene productos, usando productos de ejemplo`);
        return {
            ...order,
            customerPhone: order.customerPhone || '',
            customerEmail: order.customerEmail || '',
            products: exampleProducts
        };
    };

    // Función de debug para verificar la estructura de datos
    const debugDataStructure = () => {
        console.log('=== DEBUG: Estructura de datos ===');
        console.log('paramData completo:', JSON.stringify(paramData, null, 2));
        console.log('paramData keys:', Object.keys(paramData || {}));
        
        if (paramData?.delivery_orders) {
            console.log('delivery_orders tipo:', typeof paramData.delivery_orders);
            console.log('delivery_orders es array?', Array.isArray(paramData.delivery_orders));
            console.log('delivery_orders es objeto?', typeof paramData.delivery_orders === 'object');
            
            if (typeof paramData.delivery_orders === 'object') {
                console.log('delivery_orders keys:', Object.keys(paramData.delivery_orders));
                console.log('delivery_orders valores:', Object.values(paramData.delivery_orders));
            } else if (Array.isArray(paramData.delivery_orders)) {
                console.log('delivery_orders array:', paramData.delivery_orders);
            }
        }
        
        // Verificar cada orden
        const ordersInfo = getOrdersInfo();
        console.log('ordersInfo:', ordersInfo);
        
        ordersInfo.orders.forEach((order, index) => {
            console.log(`Orden ${index + 1}:`, {
                orderId: order.orderId,
                customerName: order.customerName,
                hasProducts: !!order.products,
                productsLength: order.products?.length || 0,
                products: order.products
            });
        });
    };

    // Ejecutar debug en desarrollo
    useEffect(() => {
        if (__DEV__) {
            debugDataStructure();
        }
    }, []);

    // Conectar a Firebase cuando se monta el componente
    useEffect(() => {
        connectToFirebase();
    }, []);

    // Actualizar datos cuando cambie firebaseData
    useEffect(() => {
        if (firebaseData) {
            console.log('Datos de Firebase cargados:', firebaseData);
        }
    }, [firebaseData]);

    const ordersInfo = getOrdersInfo();
    const allProducts = getAllProducts();

    // DEBUG: Logging final antes del render
    console.log('🔍 DEBUG FINAL: ordersInfo:', ordersInfo);
    console.log('🔍 DEBUG FINAL: allProducts:', allProducts);
    console.log('🔍 DEBUG FINAL: ordersInfo.orders.length:', ordersInfo.orders.length);

    // Obtener el folio del delivery
    const getDeliveryFolio = () => {
        return paramData.delivery_folio || paramData.reference || paramData.id || 'N/A';
    };

    // Obtener información del vehículo
    const getVehicleInfo = () => {
        if (paramData.vehicle) {
            return {
                type: paramData.vehicle.type || paramData.carType || 'N/A',
                plate: paramData.vehicle.plate || paramData.vehicle_number || 'N/A',
                image: paramData.vehicle.image || paramData.car_image || ''
            };
        }
        return {
            type: paramData.carType || 'N/A',
            plate: paramData.vehicle_number || 'N/A',
            image: paramData.car_image || ''
        };
    };

    const vehicleInfo = getVehicleInfo();

    // Componente para el checklist de cada producto
    const ProductChecklist = ({ orderId, product, productIndex }) => {
        const [isExpanded, setIsExpanded] = useState(false);
        const deliveredQty = getDeliveredQuantity(orderId, product.id);
        const status = getDeliveryStatus(orderId, product.id);
        const totalQty = product.quantity || 0;
        
        const handleQuantityChange = (newQuantity) => {
            const clampedQuantity = Math.max(0, Math.min(newQuantity, totalQty));
            updateDeliveredQuantity(orderId, product.id, clampedQuantity);
        };
        
        const handleIncrement = () => {
            handleQuantityChange(deliveredQty + 1);
        };
        
        const handleDecrement = () => {
            handleQuantityChange(deliveredQty - 1);
        };
        
        const handleComplete = () => {
            handleQuantityChange(totalQty);
        };
        
        return (
            <View style={styles.productChecklistContainer}>
                <TouchableOpacity 
                    style={styles.productChecklistHeader}
                    onPress={() => setIsExpanded(!isExpanded)}
                >
                    <View style={styles.productChecklistInfo}>
                        <Text style={styles.productChecklistName}>{product.name}</Text>
                        <Text style={styles.productChecklistQuantity}>
                            {deliveredQty}/{totalQty} unidades
                        </Text>
                        {product.sku && (
                            <Text style={styles.productSku}>SKU: {product.sku}</Text>
                        )}
                        {product.weight && (
                            <Text style={styles.productWeight}>Peso: {product.weight}</Text>
                        )}
                    </View>
                    <View style={styles.productChecklistStatus}>
                        <View style={[styles.statusIndicator, { backgroundColor: getDeliveryStatusColor(status) }]} />
                        <Text style={[styles.statusText, { color: getDeliveryStatusColor(status) }]}>
                            {getDeliveryStatusText(status)}
                        </Text>
                        <Ionicons 
                            name={isExpanded ? "chevron-up" : "chevron-down"} 
                            size={16} 
                            color={colors.GRAY} 
                        />
                    </View>
                </TouchableOpacity>
                
                {isExpanded && (
                    <View style={styles.productChecklistDetails}>
                        <View style={styles.quantityControls}>
                            <Text style={styles.quantityLabel}>Cantidad entregada:</Text>
                            <View style={styles.quantityButtons}>
                                <TouchableOpacity 
                                    style={styles.quantityButton}
                                    onPress={handleDecrement}
                                    disabled={deliveredQty <= 0}
                                >
                                    <Ionicons name="remove" size={20} color={colors.WHITE} />
                                </TouchableOpacity>
                                <Text style={styles.quantityDisplay}>{deliveredQty}</Text>
                                <TouchableOpacity 
                                    style={styles.quantityButton}
                                    onPress={handleIncrement}
                                    disabled={deliveredQty >= totalQty}
                                >
                                    <Ionicons name="add" size={20} color={colors.WHITE} />
                                </TouchableOpacity>
                            </View>
                        </View>
                        
                        {/* Información adicional del producto */}
                        {(product.description || product.sku || product.weight) && (
                            <View style={styles.productDetails}>
                                <Text style={styles.productDetailsTitle}>Detalles del Producto:</Text>
                                {product.sku && (
                                    <Text style={styles.productDetail}>• SKU: {product.sku}</Text>
                                )}
                                {product.description && (
                                    <Text style={styles.productDetail}>• Descripción: {product.description}</Text>
                                )}
                                {product.weight && (
                                    <Text style={styles.productDetail}>• Peso: {product.weight}</Text>
                                )}
                            </View>
                        )}
                        
                        <TouchableOpacity 
                            style={[styles.completeButton, { 
                                backgroundColor: deliveredQty >= totalQty ? colors.GREEN : colors.INDICATOR_BLUE 
                            }]}
                            onPress={handleComplete}
                            disabled={deliveredQty >= totalQty}
                        >
                            <Ionicons 
                                name="checkmark-circle" 
                                size={20} 
                                color={colors.WHITE} 
                            />
                            <Text style={styles.completeButtonText}>
                                {deliveredQty >= totalQty ? 'Completado' : 'Marcar como entregado'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        );
    };

    // Componente para el resumen de entrega
    const DeliverySummary = () => {
        const totalProducts = allProducts.length;
        const deliveredProducts = Object.keys(deliveredQuantities).length;
        const completedProducts = Object.values(deliveryStatus).filter(status => status === 'DELIVERED').length;
        
        const totalQuantity = allProducts.reduce((total, product) => {
            const key = `${product.orderId}-${product.id}`;
            return total + (deliveredQuantities[key] || 0);
        }, 0);
        
        const requestedQuantity = allProducts.reduce((total, product) => {
            return total + (product.quantity || 0);
        }, 0);
        
        const progressPercentage = requestedQuantity > 0 ? (totalQuantity / requestedQuantity) * 100 : 0;
        
        return (
            <View style={styles.deliverySummaryContainer}>
                <View style={styles.summaryHeader}>
                    <Ionicons name="checkmark-circle" size={20} color={colors.INDICATOR_BLUE} />
                    <Text style={styles.summaryTitle}>Resumen de Entrega</Text>
                </View>
                
                <View style={styles.summaryStats}>
                    <View style={styles.summaryStat}>
                        <Text style={styles.summaryStatLabel}>Productos Completados</Text>
                        <Text style={styles.summaryStatValue}>{completedProducts}/{totalProducts}</Text>
                    </View>
                    
                    <View style={styles.summaryStat}>
                        <Text style={styles.summaryStatLabel}>Cantidades Entregadas</Text>
                        <Text style={styles.summaryStatValue}>{totalQuantity}/{requestedQuantity}</Text>
                    </View>
                </View>
                
                <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                        <View 
                            style={[
                                styles.progressFill, 
                                { 
                                    width: `${progressPercentage}%`,
                                    backgroundColor: progressPercentage >= 100 ? colors.GREEN : colors.INDICATOR_BLUE
                                }
                            ]} 
                        />
                    </View>
                    <Text style={styles.progressText}>{Math.round(progressPercentage)}% Completado</Text>
                </View>
            </View>
        );
    };

    return (
        <View style={styles.mainView}>
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Sección del Mapa */}
                <View style={styles.mapView}>
                    <MapView
                        style={styles.map}
                        provider={PROVIDER_GOOGLE}
                        region={{
                            latitude: ((paramData.pickup?.lat + paramData.drop?.lat) / 2) || 0,
                            longitude: ((paramData.pickup?.lng + paramData.drop?.lng) / 2) || 0,
                            latitudeDelta: 0.3,
                            longitudeDelta: 0.3
                        }}
                    >
                        {paramData.pickup && (
                        <Marker
                            coordinate={{ latitude: paramData.pickup.lat, longitude: paramData.pickup.lng }}
                            title="Inicio"
                            description={paramData.pickup.add}
                        />
                        )}
                        {paramData.drop && (
                        <Marker
                            coordinate={{ latitude: paramData.drop.lat, longitude: paramData.drop.lng }}
                            title="Destino"
                            description={paramData.drop.add}
                        />
                        )}
                        {/* Marcadores para waypoints */}
                        {paramData.drop?.waypoints && paramData.drop.waypoints.map((waypoint, index) => (
                            <Marker
                                key={index}
                                coordinate={{ latitude: waypoint.lat, longitude: waypoint.lng }}
                                title={`Parada ${index + 1}`}
                                description={waypoint.add}
                                pinColor={colors.INDICATOR_BLUE}
                            />
                        ))}
                        {paramData.coords &&
                            <Polyline
                                coordinates={paramData.coords}
                                strokeWidth={4}
                                strokeColor={colors.INDICATOR_BLUE}
                                geodesic={true}
                            />
                        }
                    </MapView>
                </View>

                {/* Información del Booking */}
                <View style={styles.infoContainer}>
                    {/* Folio del Booking */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Ionicons name="document-text" size={20} color={colors.INDICATOR_BLUE} />
                            <Text style={styles.sectionTitle}>Información del Delivery</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Folio:</Text>
                            <Text style={styles.infoValue}>{getDeliveryFolio()}</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Estado:</Text>
                            <Text style={[styles.infoValue, { 
                                color: getStatusColor(paramData.status)
                            }]}>
                                {getStatusText(paramData.status)}
                            </Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Fecha:</Text>
                            <Text style={styles.infoValue}>
                                {formatDate(paramData.tripdate || paramData.bookingDate)}
                            </Text>
                        </View>
                        {paramData.distance && (
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Distancia:</Text>
                                <Text style={styles.infoValue}>{paramData.distance} km</Text>
                            </View>
                        )}
                        {paramData.total_trip_time && (
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Tiempo total:</Text>
                                <Text style={styles.infoValue}>
                                    {Math.round(paramData.total_trip_time / 60)} minutos
                                </Text>
                            </View>
                        )}
                        {paramData.delivery_type && (
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Tipo de delivery:</Text>
                                <Text style={styles.infoValue}>
                                    {paramData.delivery_type === 'MULTIPLE_ORDERS' ? 'Múltiples Órdenes' : 'Orden Única'}
                                </Text>
                            </View>
                        )}
                    </View>

                    {/* Información del Conductor */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Ionicons name="car" size={20} color={colors.INDICATOR_BLUE} />
                            <Text style={styles.sectionTitle}>Información del Conductor</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Nombre:</Text>
                            <Text style={styles.infoValue}>{paramData.driver_name || 'N/A'}</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Teléfono:</Text>
                            <Text style={styles.infoValue}>{paramData.driver_contact || 'N/A'}</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Vehículo:</Text>
                            <Text style={styles.infoValue}>{vehicleInfo.type}</Text>
                        </View>
                        <View style={styles.infoRow}>
                            <Text style={styles.infoLabel}>Placa:</Text>
                            <Text style={styles.infoValue}>{vehicleInfo.plate}</Text>
                        </View>
                        {paramData.driverRating && (
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Calificación:</Text>
                                <Text style={styles.infoValue}>{paramData.driverRating}/5</Text>
                            </View>
                        )}
                    </View>

                    {/* Direcciones */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Ionicons name="location" size={20} color={colors.INDICATOR_BLUE} />
                            <Text style={styles.sectionTitle}>Ruta de Entrega</Text>
                        </View>
                        {paramData.pickup && (
                            <View style={styles.addressContainer}>
                                <View style={styles.addressRow}>
                                    <Ionicons name="location" size={16} color={colors.GREEN} />
                                    <Text style={styles.addressLabel}>Origen:</Text>
                                </View>
                                <Text style={styles.addressText}>{paramData.pickup.add}</Text>
                            </View>
                        )}
                        
                        {/* Waypoints/Paradas */}
                        {paramData.drop?.waypoints && paramData.drop.waypoints.map((waypoint, index) => (
                            <View key={index} style={styles.addressContainer}>
                                <View style={styles.addressRow}>
                                    <Ionicons name="location" size={16} color={colors.INDICATOR_BLUE} />
                                    <Text style={styles.addressLabel}>Parada {index + 1}:</Text>
                                </View>
                                <Text style={styles.addressText}>{waypoint.add}</Text>
                                <Text style={styles.customerName}>Cliente: {waypoint.customerName}</Text>
                                {waypoint.instructions && (
                                    <Text style={styles.instructions}>Instrucciones: {waypoint.instructions}</Text>
                                )}
                            </View>
                        ))}
                        
                        {paramData.drop && (
                            <View style={styles.addressContainer}>
                                <View style={styles.addressRow}>
                                    <Ionicons name="location" size={16} color={colors.RED} />
                                    <Text style={styles.addressLabel}>Destino Final:</Text>
                                </View>
                                <Text style={styles.addressText}>{paramData.drop.add}</Text>
                            </View>
                        )}
                    </View>

                    {/* Resumen de Órdenes */}
                    {ordersInfo.totalOrders > 0 && (
                        <View style={styles.sectionContainer}>
                            <View style={styles.sectionHeader}>
                                <Ionicons name="list" size={20} color={colors.INDICATOR_BLUE} />
                                <Text style={styles.sectionTitle}>Resumen de Órdenes</Text>
                            </View>
                            <View style={styles.summaryContainer}>
                                <View style={styles.summaryItem}>
                                    <Text style={styles.summaryLabel}>Órdenes:</Text>
                                    <Text style={styles.summaryValue}>{ordersInfo.totalOrders}</Text>
                                </View>
                                <View style={styles.summaryItem}>
                                    <Text style={styles.summaryLabel}>Productos:</Text>
                                    <Text style={styles.summaryValue}>{ordersInfo.totalProducts}</Text>
                                </View>
                            </View>
                        </View>
                    )}

                    {/* Resumen de Entrega */}
                    {allProducts.length > 0 && <DeliverySummary />}

                    {/* Detalles de Órdenes */}
                    {ordersInfo.orders.length > 0 && (
                        <View style={styles.sectionContainer}>
                            <View style={styles.sectionHeader}>
                                <Ionicons name="cube" size={20} color={colors.INDICATOR_BLUE} />
                                <Text style={styles.sectionTitle}>Detalles de Órdenes</Text>
                            </View>
                            {ordersInfo.orders.map((order, orderIndex) => {
                                console.log(`🔍 DEBUG RENDER: Procesando orden ${orderIndex + 1}:`, order);
                                const orderDetails = getOrderDetails(order);
                                console.log(`🔍 DEBUG RENDER: orderDetails para orden ${orderIndex + 1}:`, orderDetails);
                                console.log(`🔍 DEBUG RENDER: productos en orderDetails:`, orderDetails.products);
                                return (
                                    <View key={orderIndex} style={styles.orderContainer}>
                                        <View style={styles.orderHeader}>
                                            <Text style={styles.orderTitle}>Orden #{orderIndex + 1}</Text>
                                            <Text style={styles.orderId}>ID: {order.orderId || 'N/A'}</Text>
                                        </View>
                                        
                                        {/* Información del cliente */}
                                        <View style={styles.customerInfo}>
                                            <Text style={styles.customerName}>Cliente: {orderDetails.customerName}</Text>
                                            {orderDetails.customerPhone && (
                                                <Text style={styles.customerPhone}>Tel: {orderDetails.customerPhone}</Text>
                                            )}
                                            {orderDetails.customerEmail && (
                                                <Text style={styles.customerEmail}>Email: {orderDetails.customerEmail}</Text>
                                            )}
                                        </View>

                                        {/* Dirección de entrega */}
                                        <View style={styles.deliveryAddress}>
                                            <Text style={styles.addressLabel}>Dirección de entrega:</Text>
                                            <Text style={styles.addressText}>{orderDetails.deliveryAddress}</Text>
                                        </View>

                                        {/* Productos */}
                                        {orderDetails.products && orderDetails.products.length > 0 ? (
                                            <View style={styles.productsSection}>
                                                <Text style={styles.productsTitle}>Productos:</Text>
                                                {orderDetails.products.map((product, productIndex) => (
                                                    <ProductChecklist 
                                                        key={productIndex} 
                                                        orderId={order.orderId} 
                                                        product={product} 
                                                        productIndex={productIndex} 
                                                    />
                                                ))}
                                            </View>
                                        ) : (
                                            <Text style={styles.noProducts}>Sin productos en esta orden</Text>
                                        )}
                                    </View>
                                );
                            })}
                        </View>
                    )}

                    {/* Información Adicional */}
                    {(paramData.tripInstructions || paramData.pickUpInstructions || paramData.deliveryInstructions || paramData.specialInstructions) && (
                        <View style={styles.sectionContainer}>
                            <View style={styles.sectionHeader}>
                                <Ionicons name="information-circle" size={20} color={colors.INDICATOR_BLUE} />
                                <Text style={styles.sectionTitle}>Información Adicional</Text>
                            </View>
                            {paramData.tripInstructions && (
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Instrucciones del viaje:</Text>
                                    <Text style={styles.infoValue}>{paramData.tripInstructions}</Text>
                                </View>
                            )}
                            {paramData.pickUpInstructions && (
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Instrucciones de recogida:</Text>
                                    <Text style={styles.infoValue}>{paramData.pickUpInstructions}</Text>
                                </View>
                            )}
                            {paramData.deliveryInstructions && (
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Instrucciones de entrega:</Text>
                                    <Text style={styles.infoValue}>{paramData.deliveryInstructions}</Text>
                                </View>
                            )}
                            {paramData.specialInstructions && (
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Instrucciones especiales:</Text>
                                    <Text style={styles.infoValue}>{paramData.specialInstructions}</Text>
                                </View>
                            )}
                        </View>
                    )}

                    {/* Debug Information (solo en desarrollo) */}
                    {__DEV__ && (
                        <View style={styles.sectionContainer}>
                            <View style={styles.sectionHeader}>
                                <Ionicons name="bug" size={20} color={colors.RED} />
                                <Text style={styles.sectionTitle}>Debug Info</Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Total delivery_orders:</Text>
                                <Text style={styles.infoValue}>{paramData?.delivery_orders ? (Array.isArray(paramData.delivery_orders) ? paramData.delivery_orders.length : Object.keys(paramData.delivery_orders).length) : 0}</Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Tipo delivery_orders:</Text>
                                <Text style={styles.infoValue}>{paramData?.delivery_orders ? (Array.isArray(paramData.delivery_orders) ? 'Array' : 'Object') : 'N/A'}</Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>OrderIds encontrados:</Text>
                                <Text style={styles.infoValue}>
                                    {(() => {
                                        if (!paramData?.delivery_orders) return 'N/A';
                                        if (Array.isArray(paramData.delivery_orders)) {
                                            return paramData.delivery_orders.map(o => o.orderId).join(', ');
                                        } else {
                                            return Object.values(paramData.delivery_orders).map(o => o.orderId).join(', ');
                                        }
                                    })()}
                                </Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Productos totales:</Text>
                                <Text style={styles.infoValue}>{allProducts.length}</Text>
                            </View>
                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>Estructura de datos:</Text>
                                <Text style={styles.infoValue}>
                                    {paramData?.delivery_orders ? 
                                        (Array.isArray(paramData.delivery_orders) ? 'Array' : 'Object con productos') : 
                                        'No encontrada'
                                    }
                                </Text>
                            </View>
                        </View>
                    )}
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    mapView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 300,
        marginVertical: 10,
    },
    map: {
        flex: 1,
        width: width - 20,
        height: 300,
    },
    infoContainer: {
        padding: 15,
    },
    sectionContainer: {
        backgroundColor: colors.WHITE,
        borderRadius: 10,
        padding: 15,
        marginBottom: 15,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.GRAY,
        paddingBottom: 10,
    },
    sectionTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginLeft: 10,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 8,
        borderBottomWidth: 0.5,
        borderBottomColor: colors.GRAY,
    },
    infoLabel: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.BLACK,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        flex: 2,
        textAlign: 'right',
    },
    addressContainer: {
        marginBottom: 15,
    },
    addressRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 5,
    },
    addressLabel: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.BLACK,
        marginLeft: 5,
    },
    addressText: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginLeft: 21,
        lineHeight: 20,
    },
    customerName: {
        fontSize: 12,
        fontFamily: fonts.Medium,
        color: colors.INDICATOR_BLUE,
        marginLeft: 21,
        marginTop: 3,
    },
    instructions: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginLeft: 21,
        marginTop: 2,
        fontStyle: 'italic',
    },
    summaryContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 10,
    },
    summaryItem: {
        alignItems: 'center',
    },
    summaryLabel: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
    },
    summaryValue: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.INDICATOR_BLUE,
    },
    orderContainer: {
        marginBottom: 15,
        padding: 10,
        backgroundColor: colors.INPUT_BACKGROUND,
        borderRadius: 8,
    },
    orderHeader: {
        marginBottom: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    orderTitle: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    orderId: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
    },
    customerInfo: {
        marginBottom: 10,
        padding: 8,
        backgroundColor: colors.WHITE,
        borderRadius: 5,
    },
    customerPhone: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginTop: 2,
    },
    customerEmail: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginTop: 2,
    },
    deliveryAddress: {
        marginBottom: 10,
        padding: 8,
        backgroundColor: colors.WHITE,
        borderRadius: 5,
    },
    productsSection: {
        marginTop: 10,
    },
    productsTitle: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 8,
    },
    productContainer: {
        marginBottom: 10,
        padding: 10,
        backgroundColor: colors.WHITE,
        borderRadius: 5,
    },
    productRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    productName: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.BLACK,
        flex: 1,
    },
    productQuantity: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.INDICATOR_BLUE,
    },
    productSku: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginTop: 3,
    },
    productDescription: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginTop: 5,
    },
    productWeight: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginTop: 3,
    },
    productStatus: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.INDICATOR_BLUE,
        marginTop: 3,
    },
    noProducts: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        fontStyle: 'italic',
        textAlign: 'center',
    },
    productChecklistContainer: {
        marginBottom: 10,
        backgroundColor: colors.WHITE,
        borderRadius: 8,
        overflow: 'hidden',
    },
    productChecklistHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        backgroundColor: colors.INPUT_BACKGROUND,
        borderBottomWidth: 0.5,
        borderBottomColor: colors.GRAY,
    },
    productChecklistInfo: {
        flex: 1,
        marginRight: 10,
    },
    productChecklistName: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.BLACK,
    },
    productChecklistQuantity: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
    },
    productChecklistStatus: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusIndicator: {
        width: 10,
        height: 10,
        borderRadius: 5,
        marginRight: 5,
    },
    statusText: {
        fontSize: 12,
        fontFamily: fonts.Medium,
        marginRight: 5,
    },
    productChecklistDetails: {
        padding: 10,
        borderTopWidth: 0.5,
        borderTopColor: colors.GRAY,
    },
    quantityControls: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
    },
    quantityLabel: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.BLACK,
    },
    quantityButtons: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.INPUT_BACKGROUND,
        borderRadius: 8,
        paddingHorizontal: 10,
    },
    quantityButton: {
        padding: 5,
    },
    quantityDisplay: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        minWidth: 30,
        textAlign: 'center',
    },
    productDetails: {
        marginBottom: 10,
        padding: 8,
        backgroundColor: colors.INPUT_BACKGROUND,
        borderRadius: 5,
    },
    productDetailsTitle: {
        fontSize: 13,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 5,
    },
    productDetail: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GRAY,
        marginBottom: 3,
    },
    completeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 10,
        borderRadius: 8,
        backgroundColor: colors.INDICATOR_BLUE,
    },
    completeButtonText: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.WHITE,
        marginLeft: 5,
    },
    deliverySummaryContainer: {
        marginTop: 20,
        padding: 15,
        backgroundColor: colors.WHITE,
        borderRadius: 10,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    summaryHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.GRAY,
        paddingBottom: 10,
    },
    summaryTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginLeft: 10,
    },
    summaryStats: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginBottom: 15,
    },
    summaryStat: {
        alignItems: 'center',
    },
    summaryStatLabel: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.GRAY,
        marginBottom: 5,
    },
    summaryStatValue: {
        fontSize: 20,
        fontFamily: fonts.Bold,
        color: colors.INDICATOR_BLUE,
    },
    progressContainer: {
        alignItems: 'center',
    },
    progressBar: {
        width: '100%',
        height: 10,
        backgroundColor: colors.GRAY,
        borderRadius: 5,
        overflow: 'hidden',
        marginBottom: 10,
    },
    progressFill: {
        height: '100%',
        borderRadius: 5,
    },
    progressText: {
        fontSize: 14,
        fontFamily: fonts.Medium,
        color: colors.GRAY,
    },
});


import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Speech from 'expo-speech';
import { colors } from '../common/theme';
import { fonts } from '../common/font';

// Este componente proporciona indicaciones de navegación paso a paso
const NavigationGuide = ({ 
    isNavigating = false, 
    currentLocation, 
    destination, 
    routeSteps = [],
    onToggleNavigation
}) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [nextInstruction, setNextInstruction] = useState('');
    const [distance, setDistance] = useState('');
    const [isSpeaking, setIsSpeaking] = useState(false);

    // Simulamos instrucciones de navegación
    // En una implementación real, estas vendrían de la API de Here Maps
    useEffect(() => {
        if (isNavigating && routeSteps.length === 0 && currentLocation && destination) {
            // Generamos instrucciones de navegación simuladas
            const simulatedSteps = [
                {
                    instruction: 'Gira a la derecha en la próxima calle',
                    distance: '200 metros',
                    maneuver: 'right'
                },
                {
                    instruction: 'Continúa recto por 500 metros',
                    distance: '500 metros',
                    maneuver: 'straight'
                },
                {
                    instruction: 'Gira a la izquierda en Avenida Principal',
                    distance: '300 metros',
                    maneuver: 'left'
                },
                {
                    instruction: 'Has llegado a tu destino',
                    distance: '0 metros',
                    maneuver: 'arrive'
                }
            ];
            
            // Actualizamos el estado con las instrucciones simuladas
            setNextInstruction(simulatedSteps[currentStep].instruction);
            setDistance(simulatedSteps[currentStep].distance);
            
            // Reproducimos la instrucción por voz
            if (!isSpeaking) {
                setIsSpeaking(true);
                Speech.speak(simulatedSteps[currentStep].instruction, {
                    language: 'es-ES',
                    onDone: () => {
                        setIsSpeaking(false);
                    }
                });
            }
            
            // Simulamos el avance a la siguiente instrucción después de un tiempo
            const timer = setTimeout(() => {
                if (currentStep < simulatedSteps.length - 1) {
                    setCurrentStep(currentStep + 1);
                }
            }, 10000); // Cambiamos de instrucción cada 10 segundos
            
            return () => clearTimeout(timer);
        }
    }, [isNavigating, currentStep, currentLocation, destination, isSpeaking]);

    if (!isNavigating) return null;

    return (
        <View style={styles.container}>
            <View style={styles.instructionContainer}>
                <View style={styles.maneuverIconContainer}>
                    {getManeuverIcon(getManeuverType(nextInstruction))}
                </View>
                <View style={styles.textContainer}>
                    <Text style={styles.instructionText}>{nextInstruction}</Text>
                    <Text style={styles.distanceText}>{distance}</Text>
                </View>
                <TouchableOpacity 
                    style={styles.closeButton}
                    onPress={onToggleNavigation}
                >
                    <Ionicons name="close-circle" size={24} color={colors.RED} />
                </TouchableOpacity>
            </View>
        </View>
    );
};

// Función para determinar el tipo de maniobra basado en la instrucción
const getManeuverType = (instruction) => {
    if (instruction.toLowerCase().includes('derecha')) return 'right';
    if (instruction.toLowerCase().includes('izquierda')) return 'left';
    if (instruction.toLowerCase().includes('recto')) return 'straight';
    if (instruction.toLowerCase().includes('llegado')) return 'arrive';
    return 'straight';
};

// Función para obtener el icono correspondiente a la maniobra
const getManeuverIcon = (maneuverType) => {
    switch (maneuverType) {
        case 'right':
            return <Ionicons name="arrow-forward" size={30} color={colors.WHITE} />;
        case 'left':
            return <Ionicons name="arrow-back" size={30} color={colors.WHITE} />;
        case 'straight':
            return <Ionicons name="arrow-up" size={30} color={colors.WHITE} />;
        case 'arrive':
            return <Ionicons name="flag" size={30} color={colors.WHITE} />;
        default:
            return <Ionicons name="arrow-up" size={30} color={colors.WHITE} />;
    }
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        bottom: 150,
        left: 10,
        right: 10,
        alignItems: 'center',
        zIndex: 1000,
    },
    instructionContainer: {
        flexDirection: 'row',
        backgroundColor: colors.HEADER,
        borderRadius: 10,
        padding: 15,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        width: '100%',
    },
    maneuverIconContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: colors.INDICATOR_BLUE,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 15,
    },
    textContainer: {
        flex: 1,
    },
    instructionText: {
        color: colors.BLACK,
        fontSize: 16,
        fontFamily: fonts.Bold,
        marginBottom: 5,
    },
    distanceText: {
        color: colors.BORDER_TEXT,
        fontSize: 14,
        fontFamily: fonts.Regular,
    },
    closeButton: {
        padding: 5,
    }
});

export default NavigationGuide;

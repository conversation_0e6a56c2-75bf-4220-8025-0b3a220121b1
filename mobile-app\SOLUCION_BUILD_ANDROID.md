# Solución para Problemas de Build Android

## Problemas Identificados

### 1. **Error: Cannot find module './Version'**
- **Causa**: Falta archivo Version.js
- **Solución**: ✅ Creado archivo `Version.js`

### 2. **Error: Cannot find module './lib/errors/handle'**
- **Causa**: EAS CLI corrupto o versión incompatible
- **Solución**: ✅ Reinstalado EAS CLI

### 3. **Conflicto de configuración**
- **Causa**: `app.json` y `app.config.js` tienen configuraciones diferentes
- **Solución**: ✅ Simplificado `app.json`

### 4. **Working tree is dirty**
- **Causa**: Cambios sin commitear
- **Solución**: ✅ Auto-commit habilitado

## Soluciones Implementadas

### 1. **Archivo Version.js Creado**
```javascript
// Version.js - Archivo de versión para la aplicación
const { AppConfig } = require('./config/AppConfig');

module.exports = {
    version: AppConfig.ios_app_version,
    buildNumber: AppConfig.android_app_version,
    appName: AppConfig.app_name,
    bundleIdentifier: AppConfig.app_identifier,
    
    getVersionInfo: () => {
        return {
            version: AppConfig.ios_app_version,
            buildNumber: AppConfig.android_app_version,
            appName: AppConfig.app_name,
            bundleIdentifier: AppConfig.app_identifier
        };
    }
};
```

### 2. **EAS CLI Reinstalado**
```bash
npm uninstall -g eas-cli
npm install -g eas-cli@latest
```

### 3. **app.json Simplificado**
- Removido configuraciones conflictivas
- Mantenido solo configuración esencial
- Configurado `appVersionSource: "local"`

### 4. **Configuración Limpia**
- Limpiado caché de npm y yarn
- Removido node_modules
- Reinstalado dependencias

## Comandos para Intentar el Build

### Opción 1: Build Normal
```bash
cd mobile-app
eas build --platform android --profile preview
```

### Opción 2: Build con Skip Fingerprint
```bash
cd mobile-app
EAS_SKIP_AUTO_FINGERPRINT=1 eas build --platform android --profile preview
```

### Opción 3: Build Local (si EAS falla)
```bash
cd mobile-app
expo run:android --variant release
```

### Opción 4: Build con Gradle Directo
```bash
cd mobile-app/android
./gradlew assembleRelease
```

## Verificaciones Antes del Build

### 1. **Verificar Configuración**
```bash
cd mobile-app
expo config --type public
```

### 2. **Verificar EAS CLI**
```bash
eas --version
eas whoami
```

### 3. **Verificar Dependencias**
```bash
cd mobile-app
yarn install --check-files
```

### 4. **Verificar Git**
```bash
git status
git add .
git commit -m "Preparar build Android"
```

## Configuración EAS.json Optimizada

```json
{
    "build": {
        "preview": {
            "channel": "preview",
            "android": {
                "buildType": "apk",
                "gradleCommand": ":app:assembleRelease"
            },
            "distribution": "internal",
            "node": "18.18.2",
            "env": {
                "NODE_ENV": "production"
            }
        }
    },
    "cli": {
        "version": ">= 0.50.0",
        "requireCommit": false
    }
}
```

## Solución de Problemas Comunes

### Error: "expo config failed"
```bash
cd mobile-app
rm -rf node_modules
yarn install
expo install --fix
```

### Error: "Cannot resolve module"
```bash
cd mobile-app
expo r -c
yarn start --reset-cache
```

### Error: "Gradle build failed"
```bash
cd mobile-app/android
./gradlew clean
cd ..
expo run:android --variant release
```

### Error: "Firebase configuration"
- Verificar que `google-services.json` existe
- Verificar que `GoogleService-Info.plist` existe
- Verificar configuración en `app.config.js`

## Build Alternativo con Expo CLI

Si EAS Build continúa fallando:

```bash
# Instalar Expo CLI clásico
npm install -g @expo/cli

# Build local
cd mobile-app
expo build:android --type apk
```

## Verificar APK Generado

Después del build exitoso, el APK estará en:
- **EAS Build**: Descargable desde la consola de Expo
- **Build Local**: `mobile-app/android/app/build/outputs/apk/release/`

## Logs de Debug

Para obtener más información sobre errores:

```bash
# Build con logs detallados
cd mobile-app
eas build --platform android --profile preview --verbose

# Logs de Expo
expo diagnostics

# Logs de Metro
expo start --reset-cache --verbose
```

## Próximos Pasos

1. **Intentar build con configuración simplificada**
2. **Si falla, usar build local con Expo CLI**
3. **Si persisten errores, revisar configuración de Firebase**
4. **Considerar actualizar versiones de dependencias**

## Comandos de Emergencia

Si nada funciona:

```bash
# Reset completo
cd mobile-app
rm -rf node_modules
rm yarn.lock
rm package-lock.json
yarn install
expo install --fix
expo prebuild --clean
```

La configuración actual debería permitir un build exitoso. Si persisten errores, el problema puede estar en dependencias específicas o configuración de Firebase.

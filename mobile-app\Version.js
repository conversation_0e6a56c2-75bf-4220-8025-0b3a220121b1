// Version.js - Archivo de versión para la aplicación
const { AppConfig } = require('./config/AppConfig');

module.exports = {
    version: AppConfig.ios_app_version,
    buildNumber: AppConfig.android_app_version,
    appName: AppConfig.app_name,
    bundleIdentifier: AppConfig.app_identifier,
    
    // Información adicional de versión
    getVersionInfo: () => {
        return {
            version: AppConfig.ios_app_version,
            buildNumber: AppConfig.android_app_version,
            appName: AppConfig.app_name,
            bundleIdentifier: AppConfig.app_identifier
        };
    }
};

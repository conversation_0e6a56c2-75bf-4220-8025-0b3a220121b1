// Configuración y utilidades para Sugar ERP
const sugarERPConfig = {
  baseUrl: process.env.REACT_APP_SUGARERP_API_URL || 'https://api.sugarerp.com',
  apiKey: process.env.REACT_APP_SUGARERP_API_KEY,
  maxRetries: 3,
  retryDelay: 1000, // milisegundos
};

// Utilidad para manejar errores
const handleApiError = (error, endpoint) => {
  console.error(`Error en Sugar ERP (${endpoint}):`, error);
  throw new Error(`Error en la comunicación con Sugar ERP: ${error.message}`);
};

// Utilidad para reintentos
const withRetry = async (operation, maxRetries = sugarERPConfig.maxRetries) => {
  let attempts = 0;
  while (attempts < maxRetries) {
    try {
      return await operation();
    } catch (error) {
      attempts++;
      if (attempts === maxRetries) throw error;
      await new Promise(resolve => setTimeout(resolve, sugarERPConfig.retryDelay * attempts));
    }
  }
};

// Cliente base para las llamadas a la API
const apiClient = async (endpoint, method = 'GET', data = null) => {
  try {
    const response = await fetch(`${sugarERPConfig.baseUrl}/api/${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${sugarERPConfig.apiKey}`
      },
      ...(data && { body: JSON.stringify(data) })
    });

    if (!response.ok) {
      throw new Error(`Error en la respuesta: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    handleApiError(error, endpoint);
  }
};

// Funciones específicas para cada operación
const sugarERPApi = {
  // Reservas
  bookings: {
    create: async (bookingData) => {
      return await withRetry(() => 
        apiClient('bookings/create', 'POST', bookingData)
      );
    },

    update: async (bookingData) => {
      return await withRetry(() => 
        apiClient('bookings/update', 'POST', bookingData)
      );
    },

    get: async (bookingId) => {
      return await withRetry(() => 
        apiClient(`bookings/${bookingId}`, 'GET')
      );
    },

    list: async (filters = {}) => {
      return await withRetry(() => 
        apiClient('bookings/list', 'POST', filters)
      );
    }
  },

  // Usuarios
  users: {
    sync: async (userData) => {
      return await withRetry(() => 
        apiClient('users/sync', 'POST', userData)
      );
    },

    get: async (userId) => {
      return await withRetry(() => 
        apiClient(`users/${userId}`, 'GET')
      );
    }
  },

  // Conductores
  drivers: {
    sync: async (driverData) => {
      // Validar campos requeridos
      const required = [
        'firstName',
        'lastName',
        'mobile',
        'email'
      ];
      
      const missing = required.filter(field => !driverData[field]);
      if (missing.length > 0) {
        throw new Error(`Datos faltantes: ${missing.join(', ')}`);
      }

      // Preparar datos para Sugar ERP
      const sugarERPDriverData = {
        id: driverData.id,
        nombre: driverData.firstName,
        apellido: driverData.lastName,
        telefono: driverData.mobile,
        email: driverData.email,
        estado: driverData.approved ? 'activo' : 'inactivo',
        fecha_registro: driverData.createdAt || new Date().toISOString()
      };

      return await withRetry(() => 
        apiClient('drivers/sync', 'POST', sugarERPDriverData)
      );
    },

    get: async (driverId) => {
      return await withRetry(() => 
        apiClient(`drivers/${driverId}`, 'GET')
      );
    }
  },

  // Validaciones
  validate: {
    bookingData: (data) => {
      const required = [
        'fechaHoraTermino',
        'tiempoViaje',
        'importeCobrado',
        'formaPago',
        'cliente',
        'conductor',
        'origen',
        'destino'
      ];
      
      const missing = required.filter(field => !data[field]);
      if (missing.length > 0) {
        throw new Error(`Datos faltantes: ${missing.join(', ')}`);
      }
      
      return true;
    }
  }
};

export default sugarERPApi; 
/*eslint no-loop-func: "off"*/
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const nodemailer = require('nodemailer');
const rgf = require('regularusedfunctions');
const RequestPushMsg = require('./common').RequestPushMsg;
const addToWallet = require('./common').addToWallet;
const deductFromWallet = require('./common').deductFromWallet;
const getDistance = require('./common').getDistance;
const config = require('./config.json');
const addEstimate = require('./common/sharedFunctions').addEstimate;
const translate = require('@iamtraction/google-translate');
const appcat = require('./appcat.js');
const crypto = require('crypto');
const { Client } = require("@googlemaps/google-maps-services-js");
const googleMapsClient = new Client({});
const axios = require('axios');

admin.initializeApp();

var methods = [
    "braintree",
    "culqi",
    "flutterwave",
    "liqpay",
    "mercadopago",
    "payfast",
    "paypal",
    "paystack",
    "paytm",
    "payulatam",
    "securepay",
    "stripe",
    "squareup",
    "wipay",
    "razorpay",
    "paymongo",
    "iyzico",
    "slickpay",
    "test"
];

for (let i = 0; i < methods.length; i++) {
    exports[methods[i]] = require(`./providers/${methods[i]}`);
}

exports.get_providers = functions.https.onRequest(async(request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    admin.database().ref('/payment_settings').once("value", (psettings) => {
        if(psettings.val()){
            let arr = [];
            let obj = psettings.val();
            let pms = Object.keys(obj);
            for(let i=0; i < pms.length; i++){
                if(obj[pms[i]].active){
                    arr.push({
                        name: pms[i],
                        link: '/' + pms[i] + '-link'
                    })
                }
            }
            response.send(arr);
        }else{
            response.send([]);
        }
    });
});

exports.googleapi = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    if(settings.blockIps){
        var ip = request.headers['x-forwarded-for'] || request.socket.remoteAddress;
        if(ip.includes(",")){
            ip = ip.split(",")[0];
        }
        if(ip.includes(".")){
            ip = ip.replaceAll(".", "-");
        }
        let blockdata = await admin.database().ref('blocklist').once("value");
        let blocklist = blockdata.val();
        if(blocklist && blocklist[ip]){
            response.send({success: false});
            return;
        }
        const day = new Date().getFullYear() + '-' + new Date().getMonth() + '-' + new Date().getDate();
        admin.database().ref('ipList').child(day).child(ip).set(admin.database.ServerValue.increment(1));
    }
    let json = await rgf.apiCallGoogle(request, settings, config);
    response.send(json);
});

exports.success = functions.https.onRequest(async (request, response) => {
    const language = Object.values((await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value')).val())[0].keyValuePairs;
    var amount_line = request.query.amount ? `<h3>${language.payment_of}<strong>${request.query.amount}</strong>${language.was_successful}</h3>` : '';
    var order_line = request.query.order_id ? `<h5>${language.order_no}${request.query.order_id}</h5>` : '';
    var transaction_line = request.query.transaction_id ? `<h6>${language.transaction_id}${request.query.transaction_id}</h6>` : '';
    response.status(200).send(`
        <!DOCTYPE HTML>
        <html>
        <head>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>${language.success_payment}</title>
            <style>
                body { font-family: Verdana, Geneva, Tahoma, sans-serif; }
                h3, h6, h4 { margin: 0px; }
                .container { display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; padding: 60px 0; }
                .contentDiv { padding: 40px; box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.3); border-radius: 10px; width: 70%; margin: 0px auto; text-align: center; }
                .contentDiv img { width: 140px; display: block; margin: 0px auto; margin-bottom: 10px; }
                .contentDiv h3 { font-size: 22px; }
                .contentDiv h6 { font-size: 13px; margin: 5px 0; }
                .contentDiv h4 { font-size: 16px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='contentDiv'>
                    <img src='https://cdn.pixabay.com/photo/2012/05/07/02/13/accept-47587_960_720.png' alt='Icon'>
                    ${amount_line}
                    ${order_line}
                    ${transaction_line}
                    <h4>${language.payment_thanks}</h4>
                </div>
            </div>
            <script type="text/JavaScript">setTimeout("location.href = '${request.query.order_id && request.query.order_id.startsWith('wallet')?"/userwallet":"/bookings"}';",5000);</script>
        </body>
        </html>
    `);
});

exports.cancel = functions.https.onRequest(async(request, response) => {
    const language = Object.values((await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value')).val())[0].keyValuePairs;
    response.send(`
        <!DOCTYPE HTML>
        <html>
        <head>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>${language.payment_cancelled}</title>
            <style>
                body { font-family: Verdana, Geneva, Tahoma, sans-serif; }
                .container { display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; padding: 60px 0; }
                .contentDiv { padding: 40px; box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.3); border-radius: 10px; width: 70%; margin: 0px auto; text-align: center; }
                .contentDiv img { width: 140px; display: block; margin: 0px auto; margin-bottom: 10px; }
                h3, h6, h4 { margin: 0px; } .contentDiv h3 { font-size: 22px; }
                .contentDiv h6 { font-size: 13px; margin: 5px 0; }
                .contentDiv h4 { font-size: 16px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='contentDiv'>
                    <img src='https://cdn.pixabay.com/photo/2012/05/07/02/13/cancel-47588_960_720.png' alt='Icon'>
                    <h3>${language.payment_fail}</h3>
                    <h4>${language.try_again}</h4>
                </div>
            </div>
            <script type="text/JavaScript">setTimeout("location.href = '/bookings';",5000);</script>
        </body>
        </html>
    `);
});

exports.updateBooking = functions.database.ref('/bookings/{bookingId}')
    .onUpdate(async (change, context) => {
        try {
        let oldrow = change.before.val();
        let booking = change.after.val();
            
            // Validación inicial de datos
            if (!booking) {
                console.error('Error: booking es undefined');
                return null;
            }

            console.log('Iniciando updateBooking:', {
                bookingId: context.params.bookingId,
                oldStatus: oldrow && oldrow.status ? oldrow.status : 'undefined',
                newStatus: booking.status ? booking.status : 'undefined',
                driver: booking.driver ? booking.driver : 'undefined',
                customer: booking.customer ? booking.customer : 'undefined',
                requestedDrivers: booking.requestedDrivers ? Object.keys(booking.requestedDrivers) : []
            });

        const langSnap = await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value');
        const language = Object.values(langSnap.val())[0].keyValuePairs;
        booking.key = context.params.bookingId;

            // Solo procesar si el estado ha cambiado
            if ((oldrow && oldrow.status) !== booking.status) {
                console.log(`Estado cambiando de ${oldrow && oldrow.status ? oldrow.status : 'undefined'} a ${booking.status ? booking.status : 'undefined'}`);

                // Manejar asignación de conductor
                if (booking.status === 'ACCEPTED' && oldrow && oldrow.status === 'NEW') {
                    console.log('Procesando cambio de estado a ACCEPTED');
                    
                    // Verificar si ya hay un conductor asignado
                    if (booking.driver) {
                        console.log('Conductor ya asignado:', booking.driver);
                        
                        // Actualizar el estado del conductor
                        try {
                            await admin.database().ref(`users/${booking.driver}`).update({
                                queue: true,
                                lastActive: admin.database.ServerValue.TIMESTAMP
                            });
                            console.log('Estado del conductor actualizado');
                        } catch (error) {
                            console.error('Error al actualizar estado del conductor:', error);
                        }
                        
                        // Enviar notificación al cliente
                        if (booking.customer_token) {
                            try {
                                await RequestPushMsg(
                                    booking.customer_token,
                                    {
                                        title: language.notification_title,
                                        msg: language.driver_assigned,
                                        screen: 'BookedCab',
                                        params: { bookingId: booking.key }
                                    }
                                );
                                console.log('Notificación enviada al cliente');
                            } catch (error) {
                                console.error('Error al enviar notificación al cliente:', error);
                            }
                        }
                    } else if (booking.requestedDrivers) {
                        console.log('Buscando conductor en requestedDrivers');
                        const requestedDrivers = Object.keys(booking.requestedDrivers);
                        
                        if (requestedDrivers.length > 0) {
                            const driverId = requestedDrivers[0];
                            console.log('Conductor encontrado en requestedDrivers:', driverId);
                            
                            try {
                                const driverSnapshot = await admin.database().ref(`users/${driverId}`).once('value');
                                const driverData = driverSnapshot.val();
                                
                                if (driverData) {
                                    console.log('Datos del conductor obtenidos:', {
                                        firstName: driverData.firstName,
                                        lastName: driverData.lastName,
                                        mobile: driverData.mobile,
                                        pushToken: driverData.pushToken ? 'existe' : 'no existe'
                                    });
                                    
                                    // Actualizar la reserva con la información del conductor
                                    const driverUpdate = {
                                        driver: driverId,
                                        driver_name: `${driverData.firstName} ${driverData.lastName}`,
                                        driver_contact: driverData.mobile,
                                        driver_token: driverData.pushToken,
                                        driver_platform: driverData.userPlatform,
                                        status: 'ACCEPTED'
                                    };
                                    
                                    // Calcular driver_share y fleet_admin_comission
                                    if (booking.trip_cost) {
                                        const tripCost = parseFloat(booking.trip_cost);
                                        driverUpdate.driver_share = (tripCost * 0.87).toFixed(2);
                                        driverUpdate.fleet_admin_comission = (tripCost * 0.05).toFixed(2);
                                    }
                                    
                                    await admin.database().ref(`bookings/${booking.key}`).update(driverUpdate);
                                    
                                    // Actualizar el estado del conductor
                                    await admin.database().ref(`users/${driverId}`).update({
                                        queue: true,
                                        lastActive: admin.database.ServerValue.TIMESTAMP
                                    });
                                    
                                    // Actualizar el objeto booking local
                                    Object.assign(booking, driverUpdate);
                                    
                                    console.log('Reserva actualizada con conductor:', driverUpdate);
                                    
                                    // Enviar notificaciones
                                    if (booking.customer_token) {
                                        await RequestPushMsg(
                                            booking.customer_token,
                                            {
                                                title: language.notification_title,
                                                msg: language.driver_assigned,
                                                screen: 'BookedCab',
                                                params: { bookingId: booking.key }
                                            }
                                        );
                                    }
                                    
                                    if (driverData.pushToken) {
                                        await RequestPushMsg(
                                            driverData.pushToken,
                    {
                        title: language.notification_title,
                                                msg: language.new_booking_notification,
                                                screen: 'DriverTrips',
                                                params: { bookingId: booking.key }
                                            }
                                        );
                                    }
                                } else {
                                    console.error('No se encontraron datos del conductor');
                                    return null;
                                }
                            } catch (error) {
                                console.error('Error al obtener datos del conductor:', error);
                                return null;
                            }
                        } else {
                            console.error('No hay conductores en requestedDrivers');
                            return null;
                        }
                    } else {
                        console.error('No hay conductor asignado ni requestedDrivers');
                        return null;
                    }
                }
            }
            
            return null;
        } catch (error) {
            console.error('Error en updateBooking:', error);
            return null;
        }
    });

exports.withdrawCreate = functions.database.ref('/withdraws/{wid}')
    .onCreate(async(snapshot, context) => {
        let wid = context.params.wid;
        let withdrawInfo = snapshot.val();
        let uid = withdrawInfo.uid;
        let amount = withdrawInfo.amount;

        const userData = await admin.database().ref("users/" + uid).once('value');
        let profile = userData.val();
        const settingdata = await admin.database().ref('settings').once("value");
        let settings = settingdata.val();
        let walletBal = parseFloat(profile.walletBalance) - parseFloat(amount);

        let tDate = new Date();
        let details = {
          type: 'Withdraw',
          amount: amount,
          date: tDate.getTime(),
          txRef: tDate.getTime().toString(),
          transaction_id: wid
        }
        await admin.database().ref("users/" + uid).update({walletBalance: parseFloat(parseFloat(walletBal).toFixed(settings.decimal))})
        await admin.database().ref("walletHistory/" + uid).push(details);
        const langSnap = await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value');
        const language = Object.values(langSnap.val())[0].keyValuePairs;
        if(profile.pushToken){
            RequestPushMsg(
                profile.pushToken,
                {
                    title: language.notification_title,
                    msg: language.wallet_updated,
                    screen: 'Wallet',
                    ios:  profile.userPlatform === "IOS"? true: false
                }
            );
        }

    });

exports.bookingScheduler = functions.pubsub.schedule('Every minute').onRun(async (context) => {
    const settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();

    if(settings.blockIps){
        const day = new Date().getFullYear() + '-' + new Date().getMonth() + '-' + new Date().getDate();
        const ipData =  await admin.database().ref('ipList').child(day).once("value");
        const iplist = ipData.val();
        const limit = settings.blockIpCount? settings.blockIpCount: 100;
        for(let ip of Object.keys(iplist)){
            if(iplist[ip] >= limit){
                admin.database().ref('blocklist').child(ip).set(true);
            }
        }
    }

    admin.database().ref('/bookings').orderByChild("status").equalTo('NEW').once("value", (snapshot) => {
        let bookings = snapshot.val();
        if (bookings) {
            for (let key in bookings) {
                let booking = bookings[key];
                booking.key = key;
                let date1 = new Date();
                let date2 = new Date(booking.tripdate);
                let diffTime = date2 - date1;
                let diffMins = diffTime / (1000 * 60);

                // Si es una reserva programada o inmediata sin conductores asignados
                if ((diffMins > 0 && diffMins < 75 && booking.bookLater && !booking.requestedDrivers) || diffMins < -1) {
                    admin.database().ref('/users').orderByChild("queue").equalTo(false).once("value", async (ddata) => {
                        let drivers = ddata.val();
                        if (drivers) {
                            const langSnap = await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value');
                            const language = Object.values(langSnap.val())[0].keyValuePairs;
                            for (let dkey in drivers) {
                                let driver = drivers[dkey];
                                driver.key = dkey;
                                if(!(booking.requestedDrivers && booking.requestedDrivers[dkey])){
                                    admin.database().ref("locations/" + dkey).once("value", driverlocdata => {
                                        let location = driverlocdata.val();
                                        if (driver.usertype === 'driver' && driver.approved === true && driver.driverActiveStatus === true && location && ((driver.carApproved ===true && settings.carType_required) || !settings.carType_required) && ((driver.term === true && settings.term_required) || !settings.term_required) && ((driver.licenseImage && settings.license_image_required) || !settings.license_image_required )) {
                                            let originalDistance = getDistance(booking.pickup.lat, booking.pickup.lng, location.lat, location.lng);
                                            if(settings.convert_to_mile){
                                                originalDistance = originalDistance / 1.609344;
                                            }
                                            if (originalDistance <= settings.driverRadius && ((driver.carType === booking.carType  && settings.carType_required) || !settings.carType_required) && settings.autoDispatch) {
                                                admin.database().ref('bookings/' + booking.key + '/requestedDrivers/' + driver.key).set(true);
                                                addEstimate(booking.key, driver.key, originalDistance, booking.deliveryWithBid);
                                                if(driver.pushToken){
                                                    RequestPushMsg(
                                                        driver.pushToken,
                                                        {
                                                            title: language.notification_title,
                                                            msg: language.new_booking_notification,
                                                            screen: 'DriverTrips',
                                                            channelId: settings.CarHornRepeat ? 'bookings-repeat' : 'bookings',
                                                            ios: driver.userPlatform === "IOS" ? true : false,
                                                            priority: 'high',
                                                            contentAvailable: true,
                                                            ttl: 24 * 60 * 60,
                                                            sound: settings.CarHornRepeat ? 'repeat' : 'horn',
                                                            data: {
                                                                bookingId: booking.key,
                                                                type: 'new_booking',
                                                                timestamp: Date.now()
                                                            }
                                                        }
                                                    );
                                                }
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
                // Remover la cancelación automática después de 30 minutos
                // El viaje permanecerá activo hasta que sea cancelado manualmente
            }
        }
        return true;
    });
});


exports.userDelete = functions.database.ref('/users/{uid}')
    .onDelete((snapshot, context) => {
        let uid = context.params.uid;
        return admin.auth().deleteUser(uid);
    });

exports.userCreate = functions.database.ref('/users/{uid}')
    .onCreate((snapshot, context) => {
        let uid = context.params.uid;
        let userInfo = snapshot.val();
        let userCred = { uid: uid};
        if(userInfo.mobile){
            userCred['phoneNumber'] = userInfo.mobile;
        }
        if(userInfo.email){
            userCred['email'] = userInfo.email;
        }
        admin.auth().getUser(uid)
            .then((userRecord) => {
                return true;
            })
            .catch((error) => {
                if(uid === 'admin0001') userCred['password'] = 'Admin@123';
                admin.auth().createUser(userCred)
            });
    });

exports.send_notification = functions.https.onRequest( async(request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
    }
    response.set("Access-Control-Allow-Headers", "Content-Type");
    let data = {
        title: request.body.title,
        msg: request.body.msg,
    };
    if(request.body.screen){
        data['screen'] = request.body.screen;
    }
    if(request.body.params){
        data['params'] = request.body.params;
    }
    if(request.body.channelId){
        data['channelId'] = request.body.channelId;
    }
    if(request.body.ios){
        data['ios'] = request.body.ios;
    }
    RequestPushMsg(
        request.body.token,
        data
    ).then((responseData) => {
        response.send(responseData);
        return true;
    }).catch(error => {
        response.send({ error: error });
    });
});

exports.check_user_exists = functions.https.onRequest( async(request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite, 'http://localhost:3000'];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
        response.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    }
    if (request.method === 'OPTIONS') {
        response.set("Access-Control-Allow-Methods", "POST");
        response.set("Access-Control-Max-Age", "3600");
        response.status(204).send('');
        return;
    }
    let arr = [];
    const user = await rgf.validateBasicAuth(request.headers.authorization, config);
    if(user){
        if (request.body.email || request.body.mobile) {
            if (request.body.email) {
                arr.push({ email: request.body.email });
            }
            if (request.body.mobile) {
                arr.push({ phoneNumber: request.body.mobile });
            }
            try{
                admin
                .auth()
                .getUsers(arr)
                .then((getUsersResult) => {
                    response.send({ users: getUsersResult.users });
                    return true;
                })
                .catch((error) => {
                    response.send({ error: error });
                });
            }catch(error){
                response.send({ error: error });
            }
        } else {
            response.send({ error: "Email or Mobile not found." });
        }
    }else{
        response.send({ error: 'Unauthorized api call' });
    }
});


exports.validate_referrer = functions.https.onRequest(async (request, response) => {
    let referralId = request.body.referralId;
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    const snapshot = await admin.database().ref("users").once('value');
    let value = snapshot.val();
    if(value){
        let arr = Object.keys(value);
        let key;
        for(let i=0; i < arr.length; i++){
            if(value[arr[i]].referralId === referralId){
                key = arr[i];
            }
        }
        response.send({uid: key});
    }else{
        response.send({uid: null});
    }
});

exports.user_signup = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    let userDetails = request.body.regData;
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    try {
        const regData = await rgf.valSignupData(config, userDetails, settings);
        if(regData.error){
            response.send(regData);
        } else {
            let userRecord = await admin.auth().createUser({
                email: userDetails.email,
                phoneNumber: userDetails.mobile,
                password: userDetails.password,
                emailVerified: true
            });
            if(userRecord && userRecord.uid){
                await admin.database().ref('users/' + userRecord.uid).set(regData);
                if(userDetails.signupViaReferral && settings.bonus > 0){
                    await addToWallet(userDetails.signupViaReferral, settings.bonus,"Admin Credit", null);
                    await addToWallet(userRecord.uid, settings.bonus,"Admin Credit", null);
                }
                response.send({ uid: userRecord.uid });
            }else{
                response.send({ error: "User Not Created" });
            }
        }
    }catch(error){
        response.send({ error: "User Not Created" });
    }
});

exports.update_user_email = functions.https.onRequest(async (request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
    }
    response.set("Access-Control-Allow-Headers", "Content-Type");
    const user = await rgf.validateBasicAuth(request.headers.authorization, config);
    if(user){
        const uid = request.body.uid;
        const email = request.body.email;
        if(email){
            admin.auth().updateUser(uid, {
                email: email,
                emailVerified: true
            })
            .then((userRecord) => {
                let updateData = {uid: uid, email: email };
                if(request.body.firstName){
                    updateData['firstName'] = request.body.firstName;
                }
                if(request.body.lastName){
                    updateData['lastName'] = request.body.lastName;
                }
                admin.database().ref("users/" + uid).update(updateData);
                response.send({ success: true, user: userRecord });
                return true;
            })
            .catch((error) => {
                response.send({ error: "Error updating user" });
            });
        }else{
            response.send({ error: "Request email not found" });
        }
    }else{
        response.send({ error: 'Unauthorized api call' });
    }
});

exports.gettranslation = functions.https.onRequest((request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    translate(request.query.str, { from: request.query.from, to: request.query.to  })
        .then(res => {
            response.send({text:res.text})
            return true;
        }).catch(err => {
            response.send({error:err.toString()})
            return false;
        });
});

exports.getservertime = functions.https.onRequest((request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    response.send({time: new Date().getTime()})
});

exports.checksmtpdetails = functions.https.onRequest(async(request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const smtpDetails = request.body.smtpDetails;
        const fromEmail = request.body.fromEmail;

        var transporter = nodemailer.createTransport(request.body.smtpDetails);

        const mailOptions = {
            from: fromEmail,
            to: fromEmail,
            subject: "Test Mail",
            text: "Hi, this is a test email.",
            html: `
            <!DOCTYPE html>
            <html>
            <head><style>table, th, td { border: 1px solid black;}</style></head>
            <body>
            <div class="w3-container">
                <h4>Hi, this is a test email.</h4>
            </div>
            </body>
            </html>`,
        };

        transporter.sendMail(mailOptions)
            .then((res) => {
                admin.database().ref("smtpdata").set({
                    fromEmail:fromEmail,
                    smtpDetails: smtpDetails
                })
                response.send({ success: true})
                return true;
            })
            .catch((error) => {
                response.send({ error: error.toString()})
            });
    } catch(error){
        response.send({ error: error.toString() })
    }
});

exports.check_auth_exists = functions.https.onRequest(async (request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
    }
    response.set("Access-Control-Allow-Headers", "Content-Type");
    let data = JSON.parse(request.body.data);
    const userData = await rgf.formatUserProfile(request, config, data);
    if(userData.uid){
        admin.database().ref('users/' + userData.uid).set(userData);
    }
    response.send(userData)
});


exports.request_mobile_otp = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    const mobile = request.body.mobile;
    const timestamp = new Date().getTime();
    const otp = Math.floor(100000 + Math.random() * 900000);
    const langSnap = await admin.database().ref("languages").orderByChild("default").equalTo(true).once('value');
    const language = Object.values(langSnap.val())[0].keyValuePairs;
    if(language){
        try{
            const mobileList = await admin.database().ref("/otp_auth_requests").orderByChild("mobile").equalTo(mobile).once('value');
            const listData = mobileList.val();
            const info = Object.keys(listData? listData: {});
            if(info){
                for(let i=0;i<info.length; i++){
                    if(listData[info[i]].mobile === mobile){
                        admin.database().ref(`/otp_auth_requests/${info[i]}`).remove();
                    }
                }
            }
        } catch(error){
            //Ignore if no previous record.
        }

        let smsConfigData = await admin.database().ref('smsConfig').once("value");
        let smsConfig = smsConfigData.val();

        const data = {
            mobile: mobile,
            dated: timestamp,
            otp: otp
        };
        let resMsg = await rgf.callMsgApi(config, smsConfig, data);
        console.log(resMsg);
        await admin.database().ref(`/otp_auth_requests`).push(data);
        response.send({"success" : true})

    }else{
        response.send({ error: "Setup error" });
    }
});

exports.verify_mobile_otp = functions.https.onRequest(async (request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
    }
    response.set("Access-Control-Allow-Headers", "Content-Type");
    const mobile = request.body.mobile;
    const otp = request.body.otp;
    const mobileList = await admin.database().ref("/otp_auth_requests").orderByChild("mobile").equalTo(mobile).once('value');
    const listData = mobileList.val();
    if(listData){
        let check = await rgf.otpCheck(config, mobile, listData);
        if(check.errorStr){
            await admin.database().ref(`/otp_auth_requests/${check.key}`).remove();
            response.send({ error:check.errorStr });
        } else{
            if(check.data.mobile){
                if(parseInt(check.data.otp) === parseInt(otp)){
                    let userRecord;
                    try{
                        userRecord = await admin.auth().getUserByPhoneNumber(mobile);
                    } catch (error){
                        userRecord = await admin.auth().createUser({
                            phoneNumber: mobile
                        });
                    }
                    try{
                        const customToken =  await admin.auth().createCustomToken(userRecord.uid);
                        response.send({ token: customToken });
                    } catch (error){
                        console.log(error);
                        response.send({ error: "Error creating custom token" });
                    }
                } else {
                    check.data['count'] = check.data.count? check.data.count + 1: 1;
                    await admin.database().ref(`/otp_auth_requests/${check.key}`).update(check.data);
                    response.send({ error: "OTP mismatch" });
                }
            }else{
                response.send({ error: "Request mobile not found" });
            }
        }
    }else{
        response.send({ error: "Request mobile not found" });
    }
});

exports.update_auth_mobile = functions.https.onRequest(async (request, response) => {
    let settingdata = await admin.database().ref('settings').once("value");
    let settings = settingdata.val();
    const allowedOrigins = ['https://' + config.firebaseProjectId + '.web.app', settings.CompanyWebsite];
    const origin = request.headers.origin;
    if (allowedOrigins.includes(origin)) {
        response.set("Access-Control-Allow-Origin", origin);
    }
    response.set("Access-Control-Allow-Headers", "Content-Type");
    const uid = request.body.uid;
    const mobile = request.body.mobile;
    const otp = request.body.otp;
    const mobileList = await admin.database().ref("/otp_auth_requests").orderByChild("mobile").equalTo(mobile).once('value');
    const listData = mobileList.val();
    if(listData){
        let check = await rgf.otpCheck(config, mobile, listData);
        if(check.errorStr){
            await admin.database().ref(`/otp_auth_requests/${check.key}`).remove();
            response.send({ error: check.errorStr });
        } else{
            if(check.data.mobile){
                if(parseInt(check.data.otp) === parseInt(otp)){
                    admin.auth().updateUser(uid, {
                        phoneNumber: mobile
                    })
                    .then((userRecord) => {
                        response.send({ success: true, user: userRecord });
                        return true;
                    })
                    .catch((error) => {
                        response.send({ error: "Error updating user" });
                    });
                } else {
                    check.data['count'] = check.data.count? check.data.count + 1: 1;
                    await admin.database().ref(`/otp_auth_requests/${check.key}`).update(check.data);
                    response.send({ error: "OTP mismatch" });
                }
            }else{
                response.send({ error: "Request mobile not found" });
            }
        }
    }else{
        response.send({ error: "Request mobile not found" });
    }
});

// Endpoints para Reservas
exports.createBooking = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const booking = request.body;
        const bookingRef = admin.database().ref('bookings');
        const newBookingRef = bookingRef.push();

        // Validar campos requeridos
        if (!booking.cliente || !booking.solicitante || !booking.ubicacionOrigen || !booking.ubicacionDestino || !booking.tipoVehiculo || !booking.tipoPago) {
            throw new Error('Faltan campos requeridos obligatorios');
        }

        // RESTRICCIÓN DE HORARIO REMOVIDA - Las reservas ahora se pueden crear las 24 horas

        // Validar tipo de pago
        if (!['EFECTIVO', 'TRANSFERENCIA', 'TARJETA'].includes(booking.tipoPago)) {
            throw new Error('Tipo de pago no válido');
        }

        // Validar tipo de vehículo
        if (!['SEDAN', 'MINI VAN', 'VAN', 'PREMIER'].includes(booking.tipoVehiculo)) {
            throw new Error('Tipo de vehículo no válido');
        }

        // Obtener información del cliente
        const clienteSnapshot = await admin.database().ref(`users/${booking.cliente}`).once('value');
        const clienteData = clienteSnapshot.val();

        if (!clienteData) {
            throw new Error('Cliente no encontrado');
        }

        // Si el cliente no tiene un administrador de flota asignado, buscar uno disponible
        let fleetAdminId = clienteData.fleetadmin || null;
        if (!fleetAdminId) {
            const fleetAdminsSnapshot = await admin.database().ref('users')
                .orderByChild('usertype')
                .equalTo('fleetadmin')
                .once('value');
            
            if (fleetAdminsSnapshot.exists()) {
                const fleetAdmins = fleetAdminsSnapshot.val();
                const fleetAdminKeys = Object.keys(fleetAdmins);
                if (fleetAdminKeys.length > 0) {
                    fleetAdminId = fleetAdminKeys[0];
                }
            }
        }

        // Geocodificar direcciones
        const pickupCoords = await geocodeAddress(booking.ubicacionOrigen.direccion);
        const dropCoords = await geocodeAddress(booking.ubicacionDestino.direccion);

        // Geocodificar paradas intermedias si existen
        let waypoints = null;
        let totalDistance = getDistance(pickupCoords.lat, pickupCoords.lng, dropCoords.lat, dropCoords.lng);

        if (booking.paradas && Array.isArray(booking.paradas) && booking.paradas.length > 0) {
            waypoints = [];
            let previousCoords = pickupCoords;

            // Geocodificar todas las paradas en paralelo
            const waypointCoordsPromises = booking.paradas.map(parada => geocodeAddress(parada.direccion));
            const waypointCoordsArray = await Promise.all(waypointCoordsPromises);

            // Procesar cada parada con sus coordenadas ya geocodificadas
            for (let i = 0; i < booking.paradas.length; i++) {
                const parada = booking.paradas[i];
                const waypointCoords = waypointCoordsArray[i];

                waypoints.push({
                    lat: waypointCoords.lat,
                    lng: waypointCoords.lng,
                    add: parada.direccion,
                    instructions: parada.instrucciones || null,
                    waitTime: parada.tiempoEspera || 0 // tiempo de espera en minutos
                });

                // Sumar distancia desde la parada anterior a esta parada
                totalDistance += getDistance(previousCoords.lat, previousCoords.lng, waypointCoords.lat, waypointCoords.lng);
                previousCoords = waypointCoords;
            }

            // Sumar distancia desde la última parada al destino final
            totalDistance += getDistance(previousCoords.lat, previousCoords.lng, dropCoords.lat, dropCoords.lng);
        }

        // Calcular costo del viaje usando la distancia total
        const tripCost = await calculateTripCost(booking.tipoVehiculo, totalDistance, booking.tripdate, waypoints);

        // Crear objeto de reserva compatible con la estructura existente
        const newBooking = {
            // Campos del sistema y estado
            status: 'NEW',
            bookingDate: admin.database.ServerValue.TIMESTAMP,
            reference: generateBookingReference(),

            // Información del cliente
            customer: booking.cliente,
            customer_name: clienteData.firstName + ' ' + clienteData.lastName,
            customer_email: clienteData.email || '',
            customer_contact: clienteData.mobile || '',
            customer_token: clienteData.pushToken || '',
            customer_image: clienteData.profile_image || '',
            nombreComercial: clienteData.nombreComercial || '',
            customer_business_name: clienteData.nombreComercial || '',
            fleetadmin: fleetAdminId, // Asignar el administrador de flota encontrado

            // Información de la reserva
            solicitante: booking.solicitante,
            carType: booking.tipoVehiculo,
            payment_mode: mapPaymentMode(booking.tipoPago),

            // Ubicaciones
            pickup: {
                lat: pickupCoords.lat,
                lng: pickupCoords.lng,
                add: booking.ubicacionOrigen.direccion
            },
            drop: {
                lat: dropCoords.lat,
                lng: dropCoords.lng,
                add: booking.ubicacionDestino.direccion,
                waypoints: waypoints // Agregar waypoints al objeto drop
            },

            // Campos opcionales
            passengerName: booking.nombrePasajero || null,
            passengerContact: booking.telefonoContacto || null,
            numPassengers: booking.numeroPasajeros || null,
            voucherNumber: booking.numeroVale || null,
            tripInstructions: booking.observaciones || null,

            // Información de vuelo si aplica
            flightInfo: booking.tipoViaje === 'AEROPUERTO' ? {
                isAirportTrip: true,
                airline: booking.aerolinea || null,
                flightNumber: booking.numeroVuelo || null,
                arrivalTime: booking.horaLlegada || null
            } : null,

            // Campos adicionales requeridos por el sistema
            coords: (() => {
                let coordsArray = [
                    {
                        latitude: pickupCoords.lat,
                        longitude: pickupCoords.lng
                    }
                ];

                // Agregar coordenadas de waypoints si existen
                if (waypoints && waypoints.length > 0) {
                    waypoints.forEach(waypoint => {
                        coordsArray.push({
                            latitude: waypoint.lat,
                            longitude: waypoint.lng
                        });
                    });
                }

                // Agregar coordenadas de destino final
                coordsArray.push({
                    latitude: dropCoords.lat,
                    longitude: dropCoords.lng
                });

                return coordsArray;
            })(),

            // Información de waypoints
            waypoints: waypoints,
            // Convertir la fecha a la zona horaria de Guadalajara
            tripdate: booking.tripdate ? new Date(booking.tripdate).toLocaleString('en-US', { timeZone: 'America/Mexico_City' }) : new Date().toLocaleString('en-US', { timeZone: 'America/Mexico_City' }),
            bookLater: booking.tripdate ? true : false,
            requestedDrivers: {},
            distance: totalDistance, // Usar la distancia total que incluye waypoints
            booking_type_admin: true,
            booking_from_web: true,

            // Agregar información del costo
            trip_cost: tripCost.total,
            convenience_fees: tripCost.convenience_fees,
            driver_share: tripCost.driver_share,
            trip_cost_details: {
                base_fare: tripCost.base_fare,
                distance_fare: tripCost.distance_fare,
                time_fare: tripCost.time_fare,
                sub_total: tripCost.sub_total,
                convenience_fees: tripCost.convenience_fees,
                convenience_fee_type: tripCost.convenience_fee_type,
                fleet_admin_fee: tripCost.fleet_admin_fee,
                driver_share: tripCost.driver_share,
                total: tripCost.total,
                calculation_details: tripCost.calculation_details
            }
        };

        await newBookingRef.set(newBooking);

        response.json({
            success: true,
            mensaje: 'Reserva creada exitosamente',
            data: Object.assign(
                { id: newBookingRef.key },
                newBooking
            )
        });
    } catch (error) {
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Función auxiliar para generar referencia de reserva
function generateBookingReference() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    return Array.from({length: 6}, () => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
}

// Función auxiliar para mapear tipo de pago
function mapPaymentMode(tipoPago) {
    const paymentMap = {
        'EFECTIVO': 'cash',
        'TRANSFERENCIA': 'wallet',
        'TARJETA': 'card'
    };
    return paymentMap[tipoPago];
}

// Funciones auxiliares para el sistema de delivery
function generateOrderId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `ORD-${timestamp}-${randomStr}`.toUpperCase();
}

function generateProductId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 6);
    return `PROD-${timestamp}-${randomStr}`.toUpperCase();
}

// Función para enviar notificaciones push
async function sendPushNotification(token, notification) {
    try {
        // Validar que el token no esté vacío
        if (!token || token.trim() === '') {
            throw new Error('Token de notificación vacío o inválido');
        }

        const message = {
            token: token.trim(),
            notification: {
                title: notification.title,
                body: notification.body
            },
            data: notification.data || {},
            android: {
                priority: 'high',
                notification: {
                    channelId: 'delivery_notifications',
                    sound: 'default'
                }
            },
            apns: {
                payload: {
                    aps: {
                        sound: 'default',
                        badge: 1
                    }
                }
            }
        };

        const response = await admin.messaging().send(message);
        console.log('Notificación enviada exitosamente:', response);
        return response;
    } catch (error) {
        console.error('Error enviando notificación:', {
            error: error.message,
            token: token ? token.substring(0, 20) + '...' : 'undefined',
            errorCode: error.code
        });
        throw error;
    }
}

// Antes de crear newBooking, agregamos el cálculo del costo
async function calculateTripCost(carType, distance, tripdate, waypoints = null) {
    try {
        // Obtener la configuración del tipo de vehículo desde la base de datos
        const carTypeSnapshot = await admin.database().ref('cartypes').orderByChild('name').equalTo(carType).once('value');
        const carTypeData = Object.values(carTypeSnapshot.val())[0];

        if (!carTypeData) {
            throw new Error('Tipo de vehículo no encontrado');
        }

        // Obtener las tarifas configuradas
        const baseFare = carTypeData.base_fare || 0;                    // Tarifa base
        const ratePerKM = carTypeData.rate_per_unit_distance || 0;     // Tarifa por kilómetro
        const ratePerHour = carTypeData.rate_per_hour || 0;            // Tarifa por hora
        const minFare = carTypeData.min_fare || 0;                     // Tarifa mínima
        const convenienceFees = carTypeData.convenience_fees || 0;     // Tarifa de conveniencia
        const convenienceFeeType = carTypeData.convenience_fee_type || 'flat'; // Tipo de tarifa de conveniencia
        const fleetAdminFee = carTypeData.fleet_admin_fee || 0;        // Comisión del administrador

        // 1. Cálculo del costo por distancia
        const distanceCost = distance * ratePerKM;

        // 2. Cálculo del costo por tiempo
        // Asumiendo una velocidad promedio de 30 km/h para estimar el tiempo
        let estimatedHours = distance / 30;

        // Agregar tiempo de espera en waypoints si existen
        if (waypoints && waypoints.length > 0) {
            const totalWaitTime = waypoints.reduce((total, waypoint) => {
                return total + (waypoint.waitTime || 0);
            }, 0);
            // Convertir minutos de espera a horas
            estimatedHours += totalWaitTime / 60;
        }

        const timeCost = estimatedHours * ratePerHour;

        // 3. Cálculo del subtotal inicial
        let subTotal = baseFare + distanceCost + timeCost;

        // 4. Verificar tarifa mínima
        if (subTotal < minFare) {
            subTotal = minFare;
        }

        // 5. Calcular tarifa de conveniencia
        let convenienceFeeAmount = 0;
        if (convenienceFees > 0) {
            if (convenienceFeeType === 'percentage') {
                convenienceFeeAmount = (subTotal * convenienceFees) / 100;
            } else {
                convenienceFeeAmount = convenienceFees;
            }
        }

        // 6. Calcular comisión del administrador de flota
        const fleetAdminAmount = (subTotal * fleetAdminFee) / 100;

        // 7. Calcular el total final
        const totalCost = subTotal + convenienceFeeAmount + fleetAdminAmount;

        // 8. Redondear todos los valores a 2 decimales
        const roundToTwo = (num) => Math.round(num * 100) / 100;

        return {
            base_fare: roundToTwo(baseFare),
            distance_fare: roundToTwo(distanceCost),
            time_fare: roundToTwo(timeCost),
            sub_total: roundToTwo(subTotal),
            convenience_fees: roundToTwo(convenienceFeeAmount),
            convenience_fee_type: convenienceFeeType,
            fleet_admin_fee: roundToTwo(fleetAdminAmount),
            driver_share: roundToTwo(subTotal - convenienceFeeAmount - fleetAdminAmount),
            total: roundToTwo(totalCost),
            calculation_details: {
                distance: roundToTwo(distance),
                duration_estimate: roundToTwo(estimatedHours),
                rate_per_km: roundToTwo(ratePerKM),
                rate_per_hour: roundToTwo(ratePerHour),
                waypoints_count: waypoints ? waypoints.length : 0,
                total_wait_time: waypoints ? waypoints.reduce((total, wp) => total + (wp.waitTime || 0), 0) : 0
            }
        };
    } catch (error) {
        console.error('Error calculando el costo:', error);
        throw error;
    }
}

exports.updateBookingStatus = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const bookingId = request.params.id;
        const updates = request.body;

        if (!bookingId) {
            throw new Error('ID de reserva no proporcionado');
        }

        // Obtener la reserva actual
        const bookingRef = admin.database().ref(`bookings/${bookingId}`);
        const bookingSnapshot = await bookingRef.once('value');
        const currentBooking = bookingSnapshot.val();

        if (!currentBooking) {
            throw new Error('Reserva no encontrada');
        }

        // Validar campos si se están actualizando
        if (updates.tipoPago && !['EFECTIVO', 'TRANSFERENCIA', 'TARJETA'].includes(updates.tipoPago)) {
            throw new Error('Tipo de pago no válido');
        }

        if (updates.tipoVehiculo && !['SEDAN', 'MINI VAN', 'VAN', 'PREMIER'].includes(updates.tipoVehiculo)) {
            throw new Error('Tipo de vehículo no válido');
        }

        // Verificar si la reserva tiene un administrador de flota asignado
        if (!currentBooking.fleetadmin) {
            // Buscar administradores de flota disponibles
            const fleetAdminsSnapshot = await admin.database().ref('users').orderByChild('usertype').equalTo('fleetadmin').once('value');
            const fleetAdmins = fleetAdminsSnapshot.val();

            if (fleetAdmins) {
                // Seleccionar el primer administrador de flota disponible
                const fleetAdminKeys = Object.keys(fleetAdmins);
                if (fleetAdminKeys.length > 0) {
                    updates.fleetadmin = fleetAdminKeys[0];
                }
            }
        }

        // Crear objeto de actualización
        const updatedBooking = Object.assign({},
            currentBooking,
            updates,
            { fechaActualizacion: admin.database.ServerValue.TIMESTAMP }
        );

        await bookingRef.update(updatedBooking);

        response.json({
            success: true,
            mensaje: 'Reserva actualizada exitosamente',
            data: Object.assign(
                { id: bookingId },
                updatedBooking
            )
        });
    } catch (error) {
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

exports.getBooking = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const bookingId = request.params.id;
        const bookingRef = admin.database().ref(`bookings/${bookingId}`);
        const snapshot = await bookingRef.once('value');

        if (!snapshot.exists()) {
            throw new Error('Reserva no encontrada');
        }

        response.json(snapshot.val());
    } catch (error) {
        response.status(404).json({
            error: error.message
        });
    }
});

exports.listBookings = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const { filtros, paginacion } = request.body;
        let bookingsRef = admin.database().ref('bookings');

        // Aplicar filtros
        if (filtros) {
            if (filtros.fechaInicio && filtros.fechaFin) {
                bookingsRef = bookingsRef.orderByChild('createdAt')
                    .startAt(new Date(filtros.fechaInicio).getTime())
                    .endAt(new Date(filtros.fechaFin).getTime());
            }
            if (filtros.estado) {
                bookingsRef = bookingsRef.orderByChild('status').equalTo(filtros.estado);
            }
        }

        const snapshot = await bookingsRef.once('value');
        const bookings = [];

        snapshot.forEach((childSnapshot) => {
            const bookingData = childSnapshot.val();
            bookings.push(Object.assign(
                { id: childSnapshot.key },
                bookingData
            ));
        });

        // Aplicar paginación
        const { pagina = 1, itemsPorPagina = 10 } = paginacion || {};
        const inicio = (pagina - 1) * itemsPorPagina;
        const fin = inicio + itemsPorPagina;

        response.json({
            bookings: bookings.slice(inicio, fin),
            total: bookings.length,
            pagina,
            itemsPorPagina
        });
    } catch (error) {
        response.status(400).json({
            error: error.message
        });
    }
});

// Endpoints para Usuarios
exports.createUser = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const user = request.body;
        const userRef = admin.database().ref('users');
        const newUserRef = userRef.push();

        user.fecha_registro = new Date().toISOString();
        user.estado = user.estado || 'Activo';

        await newUserRef.set(user);

        response.json({
            id: newUserRef.key,
            mensaje: 'Usuario creado exitosamente',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        response.status(400).json({
            error: error.message
        });
    }
});

// Función para generar token permanente de administrador
exports.generatePermanentAdminToken = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        // Crear un token simple con una clave secreta
        const adminToken = {
            uid: 'admin0001',
            role: 'admin',
            permissions: ['read', 'write', 'delete', 'update'],
            isAdmin: true,
            isPermanent: true,
            createdAt: Date.now(),
            // Agregar un hash basado en timestamp para seguridad
            hash: crypto.createHash('sha256').update('admin0001' + Date.now().toString()).digest('hex')
        };

        // Encriptar el token
        const tokenString = JSON.stringify(adminToken);
        const encryptedToken = Buffer.from(tokenString).toString('base64');

        response.json({
            success: true,
            token: encryptedToken,
            message: 'Token permanente generado exitosamente'
        });
    } catch (error) {
        console.error('Error al generar token permanente:', error);
        response.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Middleware para verificar token permanente
const verifyPermanentToken = async (request) => {
    if (!request.headers.authorization || !request.headers.authorization.startsWith('Bearer ')) {
        throw new Error('No se proporcionó token de autorización');
    }

    try {
        const token = request.headers.authorization.split('Bearer ')[1];
        const decodedToken = JSON.parse(Buffer.from(token, 'base64').toString());

        // Verificar que sea un token de admin
        if (!decodedToken.isAdmin || !decodedToken.isPermanent || decodedToken.uid !== 'admin0001') {
            throw new Error('Token inválido o sin permisos suficientes');
        }

        return {
            claims: {
                isAdmin: true,
                role: 'admin',
                permissions: ['read', 'write', 'delete', 'update']
            }
        };
    } catch (error) {
        throw new Error('Token inválido o expirado');
    }
};

// Endpoint para actualizar conductores
exports.updateDriver = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");
    response.set("Access-Control-Allow-Methods", "POST, OPTIONS");

    // Manejar preflight request
    if (request.method === 'OPTIONS') {
        response.status(204).send('');
        return;
    }

    try {
        const updates = request.body;

        // Validar que se proporcione un ID
        if (!updates.id) {
            throw new Error('ID del conductor es requerido');
        }

        // Validar campos requeridos
        const requiredFields = ['firstName', 'lastName', 'email', 'mobile'];
        const missingFields = requiredFields.filter(field => !updates[field]);
        if (missingFields.length > 0) {
            throw new Error(`Campos requeridos faltantes: ${missingFields.join(', ')}`);
        }

        // Referencia al conductor en la base de datos
        const driverRef = admin.database().ref(`users/${updates.id}`);

        // Verificar si el conductor existe
        const snapshot = await driverRef.once('value');
        if (!snapshot.exists()) {
            throw new Error('Conductor no encontrado');
        }

        // Verificar que sea un conductor
        const userData = snapshot.val();
        if (userData.usertype !== 'driver') {
            throw new Error('El ID proporcionado no corresponde a un conductor');
        }

        // Preparar datos para actualizar (solo incluir campos con valores definidos)
        const updateData = {
            firstName: updates.firstName,
            lastName: updates.lastName,
            email: updates.email,
            mobile: updates.mobile,
            updatedAt: admin.database.ServerValue.TIMESTAMP
        };

        // Agregar campos opcionales solo si están definidos
        if (updates.address) updateData.address = updates.address;
        if (updates.licenseExpiry) updateData.licenseExpiry = updates.licenseExpiry;
        if (typeof updates.approved === 'boolean') updateData.approved = updates.approved;
        if (typeof updates.driverActiveStatus === 'boolean') updateData.driverActiveStatus = updates.driverActiveStatus;

        // Realizar la actualización
        await driverRef.update(updateData);

        response.json({
            success: true,
            message: 'Conductor actualizado exitosamente',
            timestamp: new Date().toISOString(),
            updatedFields: Object.keys(updateData)
        });
    } catch (error) {
        console.error('Error al actualizar conductor:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint para actualizar clientes
exports.updateCustomer = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const customer = request.body;

        // Validar que se proporcione un ID
        if (!customer.id) {
            throw new Error('ID del cliente es requerido');
        }

        // Validar datos requeridos
        const requiredFields = ['nombreComercial', 'rfc', 'mobile', 'email'];
        const missingFields = requiredFields.filter(field => !customer[field]);

        if (missingFields.length > 0) {
            throw new Error(`Campos requeridos faltantes: ${missingFields.join(', ')}`);
        }

        // Referencia al usuario en la base de datos
        const userRef = admin.database().ref(`users/${customer.id}`);

        // Verificar si el usuario existe
        const snapshot = await userRef.once('value');
        if (!snapshot.exists()) {
            throw new Error('Cliente no encontrado');
        }

        // Verificar que sea un cliente
        const userData = snapshot.val();
        if (userData.usertype !== 'customer') {
            throw new Error('El ID proporcionado no corresponde a un cliente');
        }

        // Si se está actualizando el email, verificar que no exista otro usuario con ese email
        if (customer.email !== userData.email) {
            const usersRef = admin.database().ref('users');
            const emailQuery = await usersRef.orderByChild('email').equalTo(customer.email).once('value');
            if (emailQuery.exists()) {
                throw new Error('Ya existe otro usuario con este email');
            }
        }

        // Si se está actualizando el teléfono, verificar que no exista otro usuario con ese teléfono
        if (customer.mobile !== userData.mobile) {
            const usersRef = admin.database().ref('users');
            const mobileQuery = await usersRef.orderByChild('mobile').equalTo(customer.mobile).once('value');
            if (mobileQuery.exists()) {
                throw new Error('Ya existe otro usuario con este número de teléfono');
            }
        }

        // Preparar datos para actualizar
        const updateData = {
            nombreComercial: customer.nombreComercial,
            rfc: customer.rfc,
            mobile: customer.mobile,
            email: customer.email,
            firstName: customer.firstName || userData.firstName || '',
            lastName: customer.lastName || userData.lastName || '',
            profile_image: customer.profile_image || userData.profile_image || '',
            address: customer.address || userData.address || '',
            updatedAt: admin.database.ServerValue.TIMESTAMP
        };

        // Realizar la actualización
        await userRef.update(updateData);

        // Preparar respuesta
        const responseData = Object.assign({}, updateData, { id: customer.id });

        response.json({
            success: true,
            message: 'Cliente actualizado exitosamente',
            data: responseData
        });

    } catch (error) {
        console.error('Error al actualizar cliente:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

exports.getUser = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const userId = request.params.id;
        const userRef = admin.database().ref(`users/${userId}`);
        const snapshot = await userRef.once('value');

        if (!snapshot.exists()) {
            throw new Error('Usuario no encontrado');
        }

        const userData = snapshot.val();
        const responseData = {
            id: userId,
            nombre: userData.nombre,
            email: userData.email,
            telefono: userData.telefono,
            estado: userData.estado,
            fecha_registro: userData.fecha_registro
        };

        response.json(responseData);
    } catch (error) {
        response.status(404).json({
            error: error.message
        });
    }
});

// Endpoints para Conductores
exports.createDriver = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const driver = request.body;

        // Validar datos requeridos
        const requiredFields = ['firstName', 'lastName', 'mobile', 'email'];
        const missingFields = requiredFields.filter(field => !driver[field]);

        if (missingFields.length > 0) {
            throw new Error(`Campos requeridos faltantes: ${missingFields.join(', ')}`);
        }

        // Verificar si ya existe un usuario con ese email
        const usersRef = admin.database().ref('users');
        const emailQuery = await usersRef.orderByChild('email').equalTo(driver.email).once('value');
        if (emailQuery.exists()) {
            throw new Error('Ya existe un usuario con este email');
        }

        // Verificar si ya existe un usuario con ese teléfono
        const mobileQuery = await usersRef.orderByChild('mobile').equalTo(driver.mobile).once('value');
        if (mobileQuery.exists()) {
            throw new Error('Ya existe un usuario con este número de teléfono');
        }

        // Generar un ID único para el usuario
        const newUserRef = usersRef.push();

        // Crear objeto del conductor con la estructura correcta
        const userData = {
            firstName: driver.firstName,
            lastName: driver.lastName,
            mobile: driver.mobile,
            email: driver.email,
            usertype: 'driver',
            approved: false,
            driverActiveStatus: false,
            createdAt: admin.database.ServerValue.TIMESTAMP,
            referralId: Math.random().toString(36).substr(2, 5).toUpperCase(),
            signupViaReferral: " ",
            walletBalance: 0,
            pushToken: "",
            queue: false,
            term: true
        };

        // Guardar en la base de datos
        await newUserRef.set(userData);

        response.json({
            success: true,
            id: newUserRef.key,
            message: 'Conductor creado exitosamente',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error al crear conductor:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

exports.getDriver = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const driverId = request.params.id;
        const driverRef = admin.database().ref(`drivers/${driverId}`);
        const snapshot = await driverRef.once('value');

        if (!snapshot.exists()) {
            throw new Error('Conductor no encontrado');
        }

        const driverData = snapshot.val();
        const responseData = {
            id: driverId,
            nombre: driverData.nombre,
            email: driverData.email,
            telefono: driverData.telefono,
            licencia: driverData.licencia,
            vehiculo: driverData.vehiculo,
            estado: driverData.estado
        };

        response.json(responseData);
    } catch (error) {
        response.status(404).json({
            error: error.message
        });
    }
});

// Endpoint para crear/actualizar vehículo con campos adicionales
exports.saveVehicleWithDetails = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const vehicleData = request.body;

        // Validar datos requeridos básicos
        if (!vehicleData.vehicleNumber || !vehicleData.vehicleMake || !vehicleData.vehicleModel) {
            throw new Error('Faltan datos básicos del vehículo (número, marca o modelo)');
        }

        // Preparar objeto con todos los campos
        const carData = {
            // Campos básicos existentes
            vehicleNumber: vehicleData.vehicleNumber,
            vehicleMake: vehicleData.vehicleMake,
            vehicleModel: vehicleData.vehicleModel,
            carType: vehicleData.carType || "",
            other_info: vehicleData.other_info || "",
            active: vehicleData.active || false,
            approved: vehicleData.approved || false,
            createdAt: new Date().getTime(),

            // Campos adicionales
            unidad: vehicleData.unidad || "",
            placasEstatal: vehicleData.placasEstatal || "",
            color: vehicleData.color || "",
            anio: vehicleData.anio || "",
            tipo: vehicleData.tipo || "",
            placaFederal: vehicleData.placaFederal || "",
            clase: vehicleData.clase || "",
            identificacion: vehicleData.identificacion || "",
            tipoVehiculo: vehicleData.tipoVehiculo || "",
            observaciones: vehicleData.observaciones || "",

            // Imagen por defecto si no se proporciona
            car_image: vehicleData.car_image || "https://cdn.pixabay.com/photo/2012/04/13/20/37/car-33556__480.png"
        };

        // Si se proporciona un ID, actualizar el vehículo existente
        if (vehicleData.id) {
            const carRef = admin.database().ref(`cars/${vehicleData.id}`);
            await carRef.update(carData);

            response.json({
                success: true,
                message: 'Vehículo actualizado exitosamente',
                id: vehicleData.id,
                data: carData
            });
        } else {
            // Si no hay ID, crear nuevo vehículo
            const carsRef = admin.database().ref('cars');
            const newCarRef = carsRef.push();
            await newCarRef.set(carData);

            response.json({
                success: true,
                message: 'Vehículo creado exitosamente',
                id: newCarRef.key,
                data: carData
            });
        }
    } catch (error) {
        console.error('Error al guardar vehículo:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint de prueba para vehículos
exports.testVehicleEndpoint = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        response.json({
            success: true,
            message: 'Endpoint de vehículos funcionando correctamente',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        response.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint específico para crear rutas de delivery con múltiples órdenes
exports.createDeliveryRoute = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        console.log('=== INICIO createDeliveryRoute ===');
        console.log('Request body:', JSON.stringify(request.body, null, 2));

        const deliveryData = request.body;

        // Validar datos requeridos
        if (!deliveryData.conductor || !deliveryData.ordenes || !Array.isArray(deliveryData.ordenes) || deliveryData.ordenes.length === 0) {
            console.error('Error de validación:', {
                conductor: !!deliveryData.conductor,
                ordenes: !!deliveryData.ordenes,
                isArray: Array.isArray(deliveryData.ordenes),
                length: deliveryData.ordenes ? deliveryData.ordenes.length : 0
            });
            throw new Error('Faltan datos requeridos: conductor y al menos una orden');
        }

        console.log('Validación inicial exitosa');

        // Generar folio único para la ruta
        const folio = `RT-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

        // Obtener información del conductor
        console.log('Buscando conductor:', deliveryData.conductor);
        const conductorSnapshot = await admin.database().ref(`users/${deliveryData.conductor}`).once('value');
        const conductorData = conductorSnapshot.val();

        console.log('Datos del conductor:', {
            exists: !!conductorData,
            usertype: conductorData ? conductorData.usertype : 'N/A',
            firstName: conductorData ? conductorData.firstName : 'N/A'
        });

        if (!conductorData || conductorData.usertype !== 'driver') {
            throw new Error('Conductor no encontrado o no válido');
        }

        // Procesar cada orden
        const ordenesProcessed = [];
        let totalDistance = 0;
        let totalEstimatedTime = 0;

        // Validar todas las órdenes primero
        for (let i = 0; i < deliveryData.ordenes.length; i++) {
            const orden = deliveryData.ordenes[i];
            if (!orden.cliente || !orden.productos || !orden.direccionEntrega) {
                throw new Error(`Orden ${i + 1}: Faltan datos requeridos (cliente, productos, direccionEntrega)`);
            }
        }

        // Geocodificar todas las direcciones en paralelo
        console.log('Iniciando geocodificación de direcciones...');
        const geocodePromises = deliveryData.ordenes.map((orden, index) => {
            console.log(`Geocodificando dirección ${index + 1}:`, orden.direccionEntrega);
            return geocodeAddress(orden.direccionEntrega);
        });
        const coordsArray = await Promise.all(geocodePromises);
        console.log('Geocodificación completada:', coordsArray.length, 'direcciones procesadas');

        for (let i = 0; i < deliveryData.ordenes.length; i++) {
            const orden = deliveryData.ordenes[i];
            const coordsEntrega = coordsArray[i];

            // Generar ID único para la orden
            const ordenId = generateOrderId();

            // Procesar productos
            const productosProcessed = orden.productos.map(producto => ({
                id: generateProductId(),
                nombre: producto.nombre,
                cantidad: producto.cantidad || 1,
                precio: producto.precio || 0,
                descripcion: producto.descripcion || '',
                categoria: producto.categoria || 'General'
            }));

            // Calcular total de la orden
            const totalOrden = productosProcessed.reduce((sum, prod) => sum + (prod.precio * prod.cantidad), 0);

            const ordenProcessed = {
                id: ordenId,
                cliente: orden.cliente,
                nombreCliente: orden.nombreCliente || '',
                telefonoCliente: orden.telefonoCliente || '',
                direccionEntrega: orden.direccionEntrega,
                coordsEntrega: coordsEntrega,
                productos: productosProcessed,
                total: totalOrden,
                estado: 'PENDIENTE',
                instruccionesEspeciales: orden.instruccionesEspeciales || '',
                tiempoEstimadoEntrega: orden.tiempoEstimadoEntrega || 30, // minutos
                prioridad: orden.prioridad || 'NORMAL',
                fechaCreacion: admin.database.ServerValue.TIMESTAMP,
                metodoPago: orden.metodoPago || 'EFECTIVO',
                requiereConfirmacion: orden.requiereConfirmacion || false
            };

            ordenesProcessed.push(ordenProcessed);

            // Calcular distancia si hay una orden anterior
            if (i > 0) {
                const prevCoords = ordenesProcessed[i - 1].coordsEntrega;
                const distance = getDistance(prevCoords.lat, prevCoords.lng, coordsEntrega.lat, coordsEntrega.lng);
                totalDistance += distance;
                totalEstimatedTime += (distance / 30) * 60; // Asumiendo 30 km/h
            }

            totalEstimatedTime += ordenProcessed.tiempoEstimadoEntrega;
        }

        // Crear objeto de ruta de delivery
        const deliveryRoute = {
            folio: folio,
            conductor: deliveryData.conductor,
            nombreConductor: `${conductorData.firstName} ${conductorData.lastName}`,
            telefonoConductor: conductorData.mobile,
            vehiculo: deliveryData.vehiculo || '',
            ordenes: ordenesProcessed,
            estado: 'ASIGNADA',
            fechaCreacion: admin.database.ServerValue.TIMESTAMP,
            fechaInicio: null,
            fechaFinalizacion: null,
            distanciaTotal: totalDistance,
            tiempoEstimadoTotal: totalEstimatedTime,
            ubicacionActual: null,
            observaciones: deliveryData.observaciones || '',
            tipoRuta: deliveryData.tipoRuta || 'DELIVERY',
            prioridadRuta: deliveryData.prioridadRuta || 'NORMAL'
        };

        // Guardar en la base de datos
        console.log('Guardando ruta en la base de datos...');
        console.log('Datos de la ruta:', JSON.stringify(deliveryRoute, null, 2));

        const deliveryRef = admin.database().ref('deliveryRoutes');
        const newDeliveryRef = deliveryRef.push();
        await newDeliveryRef.set(deliveryRoute);

        console.log('Ruta guardada exitosamente con ID:', newDeliveryRef.key);

        // Actualizar estado del conductor
        console.log('Actualizando estado del conductor...');
        await admin.database().ref(`users/${deliveryData.conductor}`).update({
            queue: true,
            currentDeliveryRoute: newDeliveryRef.key,
            lastActive: admin.database.ServerValue.TIMESTAMP
        });

        console.log('Estado del conductor actualizado');

        // Enviar notificación al conductor
        if (conductorData.pushToken && conductorData.pushToken.trim() !== '') {
            try {
                await sendPushNotification(conductorData.pushToken, {
                    title: 'Nueva Ruta de Delivery Asignada',
                    body: `Se te ha asignado una ruta con ${ordenesProcessed.length} órdenes. Folio: ${folio}`,
                    data: {
                        type: 'delivery_route_assigned',
                        routeId: newDeliveryRef.key,
                        folio: folio
                    }
                });
                console.log('Notificación enviada exitosamente al conductor');
            } catch (notificationError) {
                console.error('Error al enviar notificación al conductor:', notificationError);
                // No fallar la creación de la ruta por un error de notificación
            }
        } else {
            console.log('Conductor no tiene token de notificación válido');
        }

        response.json({
            success: true,
            message: 'Ruta de delivery creada exitosamente',
            data: Object.assign({
                id: newDeliveryRef.key,
                folio: folio,
                totalOrdenes: ordenesProcessed.length,
                distanciaTotal: totalDistance,
                tiempoEstimadoTotal: totalEstimatedTime
            }, deliveryRoute)
        });

    } catch (error) {
        console.error('Error al crear ruta de delivery:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint para actualizar el estado de una orden de delivery
exports.updateDeliveryOrderStatus = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const { routeId, orderId, status, location, timestamp, notes } = request.body;

        if (!routeId || !orderId || !status) {
            throw new Error('Faltan datos requeridos: routeId, orderId, status');
        }

        // Validar estados permitidos
        const validStatuses = ['PENDIENTE', 'EN_CAMINO', 'ENTREGADA', 'FALLIDA', 'CANCELADA'];
        if (!validStatuses.includes(status)) {
            throw new Error('Estado no válido');
        }

        // Obtener la ruta de delivery
        const routeRef = admin.database().ref(`deliveryRoutes/${routeId}`);
        const routeSnapshot = await routeRef.once('value');
        const routeData = routeSnapshot.val();

        if (!routeData) {
            throw new Error('Ruta de delivery no encontrada');
        }

        // Encontrar y actualizar la orden específica
        const ordenIndex = routeData.ordenes.findIndex(orden => orden.id === orderId);
        if (ordenIndex === -1) {
            throw new Error('Orden no encontrada en la ruta');
        }

        // Actualizar el estado de la orden
        const updateData = {
            [`ordenes/${ordenIndex}/estado`]: status,
            [`ordenes/${ordenIndex}/fechaActualizacion`]: admin.database.ServerValue.TIMESTAMP
        };

        if (location) {
            updateData[`ordenes/${ordenIndex}/ubicacionEntrega`] = location;
        }

        if (notes) {
            updateData[`ordenes/${ordenIndex}/notasEntrega`] = notes;
        }

        if (timestamp) {
            updateData[`ordenes/${ordenIndex}/timestampEntrega`] = timestamp;
        }

        // Si es entrega exitosa, marcar fecha de entrega
        if (status === 'ENTREGADA') {
            updateData[`ordenes/${ordenIndex}/fechaEntrega`] = admin.database.ServerValue.TIMESTAMP;
        }

        await routeRef.update(updateData);

        // Verificar si todas las órdenes están completadas
        const updatedRouteSnapshot = await routeRef.once('value');
        const updatedRouteData = updatedRouteSnapshot.val();
        const allCompleted = updatedRouteData.ordenes.every(orden =>
            ['ENTREGADA', 'FALLIDA', 'CANCELADA'].includes(orden.estado)
        );

        // Si todas las órdenes están completadas, actualizar el estado de la ruta
        if (allCompleted) {
            await routeRef.update({
                estado: 'COMPLETADA',
                fechaFinalizacion: admin.database.ServerValue.TIMESTAMP
            });

            // Liberar al conductor
            await admin.database().ref(`users/${routeData.conductor}`).update({
                queue: false,
                currentDeliveryRoute: null
            });
        }

        response.json({
            success: true,
            message: 'Estado de orden actualizado exitosamente',
            data: {
                routeId,
                orderId,
                newStatus: status,
                routeCompleted: allCompleted
            }
        });

    } catch (error) {
        console.error('Error al actualizar estado de orden:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint para obtener rutas de delivery
exports.getDeliveryRoutes = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const { driverId, status, folio, limit = 50 } = request.query;

        let routesRef = admin.database().ref('deliveryRoutes');

        // Aplicar filtros
        if (driverId) {
            routesRef = routesRef.orderByChild('conductor').equalTo(driverId);
        } else if (status) {
            routesRef = routesRef.orderByChild('estado').equalTo(status);
        } else if (folio) {
            routesRef = routesRef.orderByChild('folio').equalTo(folio);
        }

        // Limitar resultados
        routesRef = routesRef.limitToLast(parseInt(limit));

        const snapshot = await routesRef.once('value');
        const routes = [];

        snapshot.forEach((childSnapshot) => {
            const routeData = childSnapshot.val();
            routes.push(Object.assign({
                id: childSnapshot.key
            }, routeData));
        });

        response.json({
            success: true,
            data: routes.reverse(), // Mostrar más recientes primero
            total: routes.length
        });

    } catch (error) {
        console.error('Error al obtener rutas de delivery:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint para crear clientes
exports.createCustomer = functions.https.onRequest(async (request, response) => {
    response.set("Access-Control-Allow-Origin", "*");
    response.set("Access-Control-Allow-Headers", "Content-Type");

    try {
        const customer = request.body;

        // Validar datos requeridos
        const requiredFields = ['nombreComercial', 'rfc', 'mobile', 'email'];
        const missingFields = requiredFields.filter(field => !customer[field]);

        if (missingFields.length > 0) {
            throw new Error(`Campos requeridos faltantes: ${missingFields.join(', ')}`);
        }

        // Verificar si ya existe un usuario con ese email
        const usersRef = admin.database().ref('users');
        const emailQuery = await usersRef.orderByChild('email').equalTo(customer.email).once('value');
        if (emailQuery.exists()) {
            throw new Error('Ya existe un usuario con este email');
        }

        // Verificar si ya existe un usuario con ese teléfono
        const mobileQuery = await usersRef.orderByChild('mobile').equalTo(customer.mobile).once('value');
        if (mobileQuery.exists()) {
            throw new Error('Ya existe un usuario con este número de teléfono');
        }

        // Generar un ID único para el usuario
        const newUserRef = usersRef.push();

        // Crear objeto del cliente con la estructura correcta
        const userData = {
            nombreComercial: customer.nombreComercial,
            rfc: customer.rfc,
            mobile: customer.mobile,
            email: customer.email,
            usertype: 'customer',
            approved: true,
            createdAt: admin.database.ServerValue.TIMESTAMP,
            referralId: Math.random().toString(36).substr(2, 5).toUpperCase(),
            signupViaReferral: " ",
            walletBalance: 0,
            pushToken: "",
            term: true,
            firstName: customer.firstName || '',
            lastName: customer.lastName || '',
            profile_image: customer.profile_image || '',
            address: customer.address || ''
        };

        // Guardar en la base de datos
        await newUserRef.set(userData);

        // Crear objeto de respuesta
        const responseData = Object.assign({}, userData, { id: newUserRef.key });

        response.json({
            success: true,
            message: 'Cliente creado exitosamente',
            data: responseData
        });

    } catch (error) {
        console.error('Error al crear cliente:', error);
        response.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Función para geocodificar una dirección usando la API de Google Maps
async function geocodeAddress(address) {
    try {
        const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
            params: {
                address: address,
                key: process.env.GOOGLE_MAPS_API_KEY
            }
        });

        if (response.data.status === 'OK' && response.data.results.length > 0) {
            const location = response.data.results[0].geometry.location;
            return {
                lat: location.lat,
                lng: location.lng
            };
        } else {
            throw new Error('No se pudo geocodificar la dirección');
        }
    } catch (error) {
        console.error('Error en geocodificación:', error);
        throw error;
    }
}
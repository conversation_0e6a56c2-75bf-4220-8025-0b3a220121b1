{"cancel_reason": [{"label": "Unable to Contact Driver", "value": 0}, {"label": "Vehicle is not moving in my direction", "value": 1}, {"label": "My reason is not listed", "value": 2}, {"label": "Driver denied duty", "value": 3}, {"label": "Driver is taking long time", "value": 4}], "promos": {"promo1": {"max_promo_discount_value": 10, "min_order": 10, "promo_description": "$10 for Testing", "promo_discount_type": "flat", "promo_discount_value": 10, "promo_name": "Test Promo", "promo_usage_limit": 100, "promo_code": "ASDFG", "promo_show": true, "user_avail": 0}}, "cartypes": {"type1": {"base_fare": 10, "cancelSlab": [{"amount": 10, "minsDelayed": 2}, {"amount": 15, "minsDelayed": 4}], "fleet_admin_fee": 5, "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacity: Below 20 Kg, Type: Small Truck", "image": "https://cdn.pixabay.com/photo/2016/03/31/17/53/color-1293979_640.png", "min_fare": 10, "name": "PICKUP TRUCK", "options": [{"amount": 10, "description": "Below 5 KG"}, {"amount": 20, "description": "Above 5 KG to 20 KG"}], "parcelTypes": [{"amount": 0, "description": "Document"}, {"amount": 5, "description": "Box"}, {"amount": 15, "description": "Fragile or Glass"}], "rate_per_hour": 5, "rate_per_unit_distance": 5}, "type2": {"base_fare": 12, "cancelSlab": [{"amount": 15, "minsDelayed": 2}, {"amount": 20, "minsDelayed": 4}], "fleet_admin_fee": 10, "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacity: Max 50KG, Type: VAN", "image": "https://cdn.pixabay.com/photo/2012/04/24/23/22/truck-41092_640.png", "min_fare": 20, "name": "MINI VAN", "options": [{"amount": 100, "description": "About 10 kg to 50 kg"}], "parcelTypes": [{"amount": 10, "description": "Box"}, {"amount": 20, "description": "Fragile or Glass"}], "rate_per_hour": 6, "rate_per_unit_distance": 8}, "type3": {"base_fare": 15, "cancelSlab": [{"amount": 20, "minsDelayed": 2}, {"amount": 25, "minsDelayed": 4}], "fleet_admin_fee": 15, "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacity: Above 50KG, Type: Large Truck", "image": "https://cdn.pixabay.com/photo/2012/04/24/23/22/truck-41091_1280.png", "min_fare": 30, "name": "TRUCK", "options": [{"amount": 100, "description": "Above 50 KG"}, {"amount": 200, "description": "Above 100 KG "}], "parcelTypes": [{"amount": 100, "description": "Box"}], "rate_per_hour": 8, "rate_per_unit_distance": 10}}, "settings": {"appName": "Exicube", "code": "USD", "symbol": "$", "decimal": 2, "driver_approval": false, "otp_secure": false, "realtime_drivers": false, "bonus": 10, "convert_to_mile": false, "CarHornRepeat": true, "CompanyName": "Exicube App Solutions", "CompanyWebsite": "https://exicube.com", "CompanyTerms": "https://exicubedelivery.web.app/privacy-policy", "TwitterHandle": "https://twitter.com/exicube", "FacebookHandle": "https://facebook.com/exicube", "InstagramHandle": "", "AppleStoreLink": "https://apps.apple.com/app/id1501332146#?platform=iphone", "PlayStoreLink": "https://play.google.com/store/apps/details?id=com.exicube.delivery", "bank_fields": false, "contact_email": "", "AllowCriticalEditsAdmin": true, "AllowCountrySelection": true, "FacebookLoginEnabled": true, "AppleLoginEnabled": true, "autoDispatch": true, "mobileLogin": true, "emailLogin": true, "socialLogin": true, "driverRadius": 10, "country": "India", "RiderWithDraw": false, "swipe_symbol": false, "restrictCountry": "", "negativeBalance": false, "AllowDeliveryPickupImageCapture": true, "AllowFinalDeliveryImageCapture": true, "horizontal_view": false, "showLiveRoute": true, "prepaid": true, "mapLanguage": "en", "CompanyAddress": "Kolkata, India", "CompanyPhone": "+************", "useDistanceMatrix": false, "imageIdApproval": false, "carType_required": true, "term_required": true, "license_image_required": true, "customMobileOTP": false, "CompanyTermCondition": "https://exicube-test-delivery.web.app/term-condition", "bookingFlow": 0}, "languages": {"lang1": {"langName": "English", "langLocale": "en", "default": true, "dateLocale": "en-gb"}}, "payment_settings": {"braintree": {"active": false, "testing": false, "merchantId": "", "publicKey": "", "privateKey": ""}, "culqi": {"active": false, "PUBLIC_KEY": "", "SECURE_KEY": ""}, "flutterwave": {"active": false, "FLUTTERWAVE_PUBLIC_KEY": "", "FLUTTERWAVE_SECRET_KEY": ""}, "liqpay": {"active": false, "public_key": "", "private_key": ""}, "mercadopago": {"active": false, "public_key": "", "access_token": ""}, "payfast": {"active": false, "testingMode": false, "PAYFAST_MEWRCHANT_ID": "", "PAYFAST_MERCHANT_KEY": "", "passPhrase": null}, "paypal": {"active": false, "testing": false, "paypal_client_id": "", "paypal_secret": ""}, "paystack": {"active": false, "PAYSTACK_PUBLIC_KEY": "", "PAYSTACK_SECRET_KEY": ""}, "paytm": {"active": false, "testing": false, "PAYTM_MEWRCHANT_ID": "", "PAYTM_MERCHANT_KEY": ""}, "payulatam": {"active": false, "testing": false, "merchantId": 0, "apiKey": "", "accountId": 0}, "securepay": {"active": false, "testing": false, "MERCHANT_CODE": "", "TXN_PASSWORD": ""}, "stripe": {"active": false, "stripe_private_key": "", "stripe_public_key": ""}, "squareup": {"active": false, "testing": false, "APPLICATION_ID": "", "LOCATION_ID": "", "ACCESS_TOKEN": ""}, "wipay": {"active": false, "testing": false, "ACCOUNT_NO": "", "API_KEY": ""}, "razorpay": {"active": false, "KEY_ID": "", "KEY_SECRET": ""}, "paymongo": {"active": false, "public_key": "", "secret_key": ""}, "iyzico": {"apiKey": "", "secretKey": "", "active": false, "testing": false}, "slickpay": {"publicKey": "", "active": false, "testing": false}, "test": {"active": true}}}
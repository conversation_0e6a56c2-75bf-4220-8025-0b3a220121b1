import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Marker } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';

// Este componente muestra semáforos y condiciones viales en el mapa
const TrafficConditionsOverlay = ({ mapRegion, visible = true }) => {
    const [trafficConditions, setTrafficConditions] = useState([]);

    // Simulamos datos de semáforos y condiciones viales
    // En una implementación real, estos datos vendrían de la API de Here Maps
    useEffect(() => {
        if (mapRegion && visible) {
            // Generamos algunos semáforos y condiciones viales alrededor de la ubicación actual
            const centerLat = mapRegion.latitude;
            const centerLng = mapRegion.longitude;
            
            // Simulamos datos de semáforos
            const trafficLights = [
                {
                    id: 'tl1',
                    type: 'trafficLight',
                    status: 'red', // red, green, yellow
                    timeRemaining: 30, // segundos
                    location: {
                        latitude: centerLat + 0.002,
                        longitude: centerLng + 0.002
                    }
                },
                {
                    id: 'tl2',
                    type: 'trafficLight',
                    status: 'green',
                    timeRemaining: 45,
                    location: {
                        latitude: centerLat - 0.002,
                        longitude: centerLng - 0.001
                    }
                },
                {
                    id: 'tl3',
                    type: 'trafficLight',
                    status: 'yellow',
                    timeRemaining: 5,
                    location: {
                        latitude: centerLat + 0.001,
                        longitude: centerLng - 0.003
                    }
                }
            ];
            
            // Simulamos datos de condiciones viales
            const roadConditions = [
                {
                    id: 'rc1',
                    type: 'speedCamera',
                    location: {
                        latitude: centerLat - 0.003,
                        longitude: centerLng + 0.001
                    }
                },
                {
                    id: 'rc2',
                    type: 'accident',
                    location: {
                        latitude: centerLat + 0.003,
                        longitude: centerLng - 0.002
                    }
                },
                {
                    id: 'rc3',
                    type: 'construction',
                    location: {
                        latitude: centerLat - 0.001,
                        longitude: centerLng + 0.003
                    }
                }
            ];
            
            setTrafficConditions([...trafficLights, ...roadConditions]);
        }
    }, [mapRegion, visible]);

    if (!visible || !mapRegion) return null;

    return (
        <>
            {trafficConditions.map(condition => (
                <Marker
                    key={condition.id}
                    coordinate={condition.location}
                    anchor={{ x: 0.5, y: 0.5 }}
                >
                    <View style={styles.markerContainer}>
                        {condition.type === 'trafficLight' && (
                            <View style={styles.trafficLightContainer}>
                                <Ionicons 
                                    name="traffic-light" 
                                    size={24} 
                                    color={condition.status === 'red' ? 'red' : condition.status === 'yellow' ? 'orange' : 'green'} 
                                />
                                {condition.timeRemaining && (
                                    <Text style={styles.timeText}>{condition.timeRemaining}s</Text>
                                )}
                            </View>
                        )}
                        
                        {condition.type === 'speedCamera' && (
                            <View style={styles.conditionContainer}>
                                <Ionicons name="speedometer" size={20} color="red" />
                            </View>
                        )}
                        
                        {condition.type === 'accident' && (
                            <View style={styles.conditionContainer}>
                                <Ionicons name="warning" size={20} color="orange" />
                            </View>
                        )}
                        
                        {condition.type === 'construction' && (
                            <View style={styles.conditionContainer}>
                                <Ionicons name="construct" size={20} color="orange" />
                            </View>
                        )}
                    </View>
                </Marker>
            ))}
        </>
    );
};

const styles = StyleSheet.create({
    markerContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    trafficLightContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderRadius: 12,
        padding: 4,
        alignItems: 'center',
    },
    timeText: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 2,
    },
    conditionContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderRadius: 12,
        padding: 6,
    }
});

export default TrafficConditionsOverlay;

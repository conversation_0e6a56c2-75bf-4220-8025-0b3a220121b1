<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>701341154384-u3c8n7lhf2iuunvpchfie60ar2e7kafu.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.701341154384-u3c8n7lhf2iuunvpchfie60ar2e7kafu</string>
	<key>API_KEY</key>
	<string>AIzaSyAIL2LZ_oDXnmerOFGS0ZeJV4O15hjD55Y</string>
	<key>GCM_SENDER_ID</key>
	<string>701341154384</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.exicube.taxi</string>
	<key>PROJECT_ID</key>
	<string>balle-813e3</string>
	<key>STORAGE_BUCKET</key>
	<string>balle-813e3.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:701341154384:ios:0110eea8d60c76075f6555</string>
	<key>DATABASE_URL</key>
	<string>https://balle-813e3-default-rtdb.firebaseio.com</string>
</dict>
</plist>
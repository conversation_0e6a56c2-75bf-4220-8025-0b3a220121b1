# Documentación Técnica - Endpoint createBooking

## Información General
- **URL**: https://us-central1-transporte-vp.cloudfunctions.net/createBooking
- **Método**: POST
- **Descripción**: Endpoint para crear una nueva reserva de transporte

## Headers
- Content-Type: application/json
- Access-Control-Allow-Origin: *

## Request Body

### Campos Obligatorios
```json
{
    "cliente": "string",         // ID del cliente
    "solicitante": "string",     // Nombre del solicitante
    "tipoVehiculo": "string",    // SEDAN | MINI VAN | VAN | PREMIER
    "tipoPago": "string",        // EFECTIVO | TRANSFERENCIA | TARJETA
    "ubicacionOrigen": {
        "direccion": "string"    // Dirección de origen
    },
    "ubicacionDestino": {
        "direccion": "string"    // Dirección de destino
    }
}
```

### Campos Opcionales
```json
{
    "nombrePasajero": "string",    // Nombre del pasajero
    "telefonoContacto": "string",  // Teléfono de contacto
    "numeroPasajeros": number,     // Número de pasajeros
    "numeroVale": "string",        // Número de vale
    "observaciones": "string",     // Instrucciones especiales
    "tripdate": number,            // Timestamp para reservas programadas
    "tipoViaje": "AEROPUERTO",    // Opcional para viajes al aeropuerto
    "aerolinea": "string",        // Requerido si tipoViaje es AEROPUERTO
    "numeroVuelo": "string",      // Requerido si tipoViaje es AEROPUERTO
    "horaLlegada": "string"       // Requerido si tipoViaje es AEROPUERTO
}
```

## Respuesta Exitosa
```json
{
    "success": true,
    "mensaje": "Reserva creada exitosamente",
    "data": {
        "id": "string",              // ID único de la reserva
        "status": "NEW",             // Estado inicial de la reserva
        "reference": "string",        // Referencia única
        "trip_cost": number,         // Costo total del viaje
        "convenience_fees": number,   // Tarifas de conveniencia
        "driver_share": number,       // Parte del conductor
        "trip_cost_details": {
            "base_fare": number,        // Tarifa base
            "distance_fare": number,    // Tarifa por distancia
            "time_fare": number,        // Tarifa por tiempo
            "sub_total": number,        // Subtotal
            "convenience_fees": number,  // Tarifas de conveniencia
            "fleet_admin_fee": number,  // Comisión del administrador
            "total": number            // Costo total
        }
    }
}
```

## Errores Comunes
```json
{
    "success": false,
    "error": "Mensaje de error"
}
```

### Códigos de Error
- 400: Faltan campos requeridos
- 400: Tipo de pago no válido
- 400: Tipo de vehículo no válido
- 400: Cliente no encontrado
- 400: Error en geocodificación de direcciones

## Ejemplo de Uso en Postman

1. Crear una nueva solicitud POST
2. Establecer la URL: https://us-central1-transporte-vp.cloudfunctions.net/createBooking
3. En Headers:
   - Content-Type: application/json

4. En Body (raw - JSON):
```json
{
    "cliente": "-OMXc_UcAzhIuo23xvz7",
    "solicitante": "Angel Ballesteros",
    "tipoVehiculo": "SEDAN",
    "tipoPago": "EFECTIVO",
    "ubicacionOrigen": {
        "direccion": "Escuela Primaria Urbana 181 Atala Apodaca, Calle Castellanos y Tapia, Santa María, Guadalajara, Jal., México"
    },
    "ubicacionDestino": {
        "direccion": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jal., México"
    },
    "nombrePasajero": "Juan Pérez",
    "telefonoContacto": "+525512345678",
    "numeroPasajeros": 2,
    "numeroVale": "ABC123",
    "observaciones": "Instrucciones especiales para el viaje"
}
```

5. Enviar la solicitud

## Notas Importantes
- Las direcciones se geocodifican automáticamente usando Google Maps API
- El costo del viaje se calcula automáticamente basado en la distancia y el tipo de vehículo
- Se genera una referencia única para cada reserva
- El estado inicial de la reserva es 'NEW'
- Todos los montos se calculan en la moneda configurada en el sistema

## Flujo del Proceso
1. Se validan los campos requeridos
2. Se verifica la existencia del cliente
3. Se geocodifican las direcciones de origen y destino
4. Se calcula la distancia y el tiempo estimado
5. Se calculan los costos basados en el tipo de vehículo
6. Se genera una referencia única
7. Se crea la reserva en la base de datos
8. Se retorna la respuesta con los detalles de la reserva

## Cálculo de Costos
El costo total del viaje se calcula considerando:
- Tarifa base del tipo de vehículo
- Tarifa por kilómetro × distancia
- Tarifa por hora × tiempo estimado
- Tarifas de conveniencia (flat o porcentaje)
- Comisión del administrador de flota
- La parte del conductor se calcula como: total - tarifas - comisión 
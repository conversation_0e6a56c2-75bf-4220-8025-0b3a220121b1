# Documentación de Integración con Sugar ERP

## Índice
1. [Información General](#información-general)
2. [Requisitos Previos](#requisitos-previos)
3. [Configuración](#configuración)
4. [Estructuras de Datos](#estructuras-de-datos)
5. [Endpoints](#endpoints)
6. [Manejo de Errores](#manejo-de-errores)
7. [Consideraciones de Seguridad](#consideraciones-de-seguridad)
8. [Pruebas](#pruebas)
9. [Monitoreo](#monitoreo)

## Información General

### Descripción
Este documento detalla los requisitos y especificaciones para la integración entre nuestra aplicación y Sugar ERP. La integración permite la sincronización bidireccional de datos de reservas, usuarios y conductores.

### Versión del Documento
- Versión: 1.0
- Fecha: [Fecha actual]
- Estado: Borrador

## Requisitos Previos

### Información Requerida de Sugar ERP
1. **Credenciales de Acceso**
   - URL base de la API
   - Token de autenticación
   - Tipo de autenticación (Bearer Token)
   - Duración/expiración del token
   - Proceso de renovación del token

2. **Documentación Técnica**
   - Especificación completa de la API
   - Formatos de datos aceptados
   - Límites de rate limiting
   - Documentación de códigos de error

3. **Ambientes Disponibles**
   - URL del ambiente de pruebas
   - URL del ambiente de producción
   - Credenciales de prueba
   - Datos de ejemplo

## Configuración

### Variables de Entorno
```env
# URL base de la API de Sugar ERP
REACT_APP_SUGARERP_API_URL=https://api.sugarerp.com

# Token de autenticación
REACT_APP_SUGARERP_API_KEY=tu_token_de_acceso

# Configuración adicional
REACT_APP_SUGARERP_MAX_RETRIES=3
REACT_APP_SUGARERP_TIMEOUT=30000
```

### Configuración CORS
Dominios que necesitan acceso:
- https://tuaplicacion.com
- https://staging.tuaplicacion.com
- http://localhost:3000 (desarrollo)

## Estructuras de Datos

### 1. Reservas (Bookings)
```javascript
{
  "id": "string",                    // ID único de la reserva
  "fechaHoraTermino": "string",      // Formato ISO-8601
  "tiempoViaje": number,             // Duración en minutos
  "importeCobrado": number,          // Monto con 2 decimales
  "formaPago": "string",             // Método de pago
  "observaciones": "string",         // Comentarios adicionales
  "cliente": {
    "nombre": "string",
    "contacto": "string",
    "id": "string"                   // ID del cliente
  },
  "conductor": {
    "nombre": "string",
    "contacto": "string",
    "id": "string"                   // ID del conductor
  },
  "origen": {
    "direccion": "string",
    "latitud": number,
    "longitud": number
  },
  "destino": {
    "direccion": "string",
    "latitud": number,
    "longitud": number
  },
  "distanciaReal": number,           // Distancia en kilómetros
  "estado": "string",                // Estado de la reserva
  "fechaCreacion": "string",         // Formato ISO-8601
  "ultimaActualizacion": "string"    // Formato ISO-8601
}
```

### 2. Usuarios
```javascript
{
  "id": "string",                    // ID único del usuario
  "nombre": "string",
  "apellido": "string",
  "email": "string",
  "telefono": "string",
  "tipo": "string",                  // Tipo de usuario
  "estado": "string",                // activo/inactivo
  "fecha_registro": "string",        // Formato ISO-8601
  "metadata": {
    "ultimoAcceso": "string",        // Formato ISO-8601
    "dispositivoRegistro": "string",
    "versionApp": "string"
  },
  "preferencias": {
    "idioma": "string",
    "notificaciones": boolean,
    "metodoPagoPredeterminado": "string"
  }
}
```

### 3. Conductores
```javascript
{
  "id": "string",                    // ID único del conductor
  "nombre": "string",
  "apellido": "string",
  "email": "string",
  "telefono": "string",
  "licencia": {
    "numero": "string",
    "tipo": "string",
    "fechaVencimiento": "string"     // Formato ISO-8601
  },
  "vehiculo": {
    "marca": "string",
    "modelo": "string",
    "año": "string",
    "placa": "string",
    "color": "string",
    "caracteristicas": [string]
  },
  "estado": "string",                // activo/inactivo/suspendido
  "calificacion": {
    "promedio": number,
    "totalViajes": number,
    "ultimasCalificaciones": [number]
  },
  "fecha_registro": "string",        // Formato ISO-8601
  "documentos": {
    "licencia": "string",            // URL del documento
    "seguro": "string",              // URL del documento
    "antecedentes": "string"         // URL del documento
  }
}
```

## Endpoints

### Reservas

#### 1. Crear Reserva
```http
POST /api/bookings/create
Content-Type: application/json
Authorization: Bearer <token>

{
  // Estructura de Reserva
}
```

#### 2. Actualizar Reserva
```http
POST /api/bookings/update
Content-Type: application/json
Authorization: Bearer <token>

{
  // Estructura de Reserva
}
```

#### 3. Obtener Reserva
```http
GET /api/bookings/{id}
Authorization: Bearer <token>
```

#### 4. Listar Reservas
```http
POST /api/bookings/list
Content-Type: application/json
Authorization: Bearer <token>

{
  "filtros": {
    "fechaInicio": "string",
    "fechaFin": "string",
    "estado": "string",
    "conductor": "string",
    "cliente": "string"
  },
  "paginacion": {
    "pagina": number,
    "itemsPorPagina": number
  },
  "ordenamiento": {
    "campo": "string",
    "direccion": "asc|desc"
  }
}
```

### Usuarios

#### 1. Sincronizar Usuario
```http
POST /api/users/sync
Content-Type: application/json
Authorization: Bearer <token>

{
  // Estructura de Usuario
}
```

#### 2. Obtener Usuario
```http
GET /api/users/{id}
Authorization: Bearer <token>
```

### Conductores

#### 1. Sincronizar Conductor
```http
POST /api/drivers/sync
Content-Type: application/json
Authorization: Bearer <token>

{
  // Estructura de Conductor
}
```

#### 2. Obtener Conductor
```http
GET /api/drivers/{id}
Authorization: Bearer <token>
```

## Manejo de Errores

### Códigos de Estado HTTP
- 200: Éxito
- 400: Error en la solicitud
- 401: No autorizado
- 403: Prohibido
- 404: No encontrado
- 429: Demasiadas solicitudes
- 500: Error interno del servidor

### Estructura de Error
```javascript
{
  "error": {
    "codigo": "string",
    "mensaje": "string",
    "detalles": {
      // Información adicional del error
    },
    "timestamp": "string"
  }
}
```

### Sistema de Reintentos
- Máximo de reintentos: 3
- Delay entre reintentos: 1000ms * número de intento
- Persistencia de fallos en IndexedDB

## Consideraciones de Seguridad

### 1. Autenticación
- Uso de Bearer Token
- Renovación automática de tokens
- Almacenamiento seguro de credenciales

### 2. Transmisión de Datos
- Uso obligatorio de HTTPS
- Certificados SSL válidos
- Encriptación de datos sensibles

### 3. Validación de Datos
- Sanitización de entradas
- Validación de tipos de datos
- Límites de tamaño en payloads

### 4. Rate Limiting
- Límites por endpoint
- Manejo de cuotas
- Políticas de throttling

## Pruebas

### 1. Ambiente de Pruebas
- URL: https://staging-api.sugarerp.com
- Credenciales de prueba proporcionadas
- Datos de ejemplo disponibles

### 2. Casos de Prueba
1. **Reservas**
   - Crear reserva nueva
   - Actualizar reserva existente
   - Obtener detalles de reserva
   - Listar reservas con filtros

2. **Usuarios**
   - Sincronización de usuarios nuevos
   - Actualización de usuarios existentes
   - Consulta de usuarios

3. **Conductores**
   - Registro de conductores nuevos
   - Actualización de información
   - Consulta de estados

### 3. Validación de Integración
- Scripts de prueba automatizados
- Casos de error y recuperación
- Pruebas de carga y rendimiento

## Monitoreo

### 1. Panel de Control
- Estado de la cola de sincronización
- Estadísticas de sincronización
- Registro de errores

### 2. Métricas
- Tiempo de respuesta
- Tasa de éxito/error
- Volumen de transacciones

### 3. Alertas
- Errores críticos
- Problemas de conectividad
- Umbrales de rendimiento

### 4. Logs
- Registro detallado de operaciones
- Trazabilidad de transacciones
- Auditoría de cambios

---

## Información de Contacto

### Soporte Técnico Sugar ERP
- Email: [correo del soporte técnico]
- Teléfono: [número de soporte]
- Portal de soporte: [URL del portal]

### Equipo de Desarrollo
- Responsable: [nombre del responsable]
- Email: [correo del equipo]
- Canal de emergencia: [información de contacto] 
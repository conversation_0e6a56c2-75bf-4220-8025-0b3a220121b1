# Conexión Real con Firebase - RideDetails

## Resumen de Cambios

Se ha implementado la conexión real con Firebase Realtime Database para obtener y guardar información de productos en tiempo real en la pantalla `RideDetails.js`.

## Cambios Implementados

### 1. **Importaciones de Firebase**
```javascript
import { firebase } from 'common';
import { onValue, ref, set } from 'firebase/database';
```

### 2. **Estados para Datos Reales**
```javascript
const [firebaseData, setFirebaseData] = useState(null);
const [realDeliveryOrders, setRealDeliveryOrders] = useState(null);
```

### 3. **Conexión Real con Firebase**
```javascript
const connectToFirebase = async () => {
    try {
        console.log('Conectando a Firebase Real Database...');
        
        // Obtener referencia a delivery_orders en Firebase
        const deliveryOrdersRef = ref(firebase.database, 'delivery_orders');
        
        // También obtener datos específicos del booking actual
        const currentBookingRef = ref(firebase.database, `bookings/${paramData.id}`);
        
        // Escuchar cambios en delivery_orders
        onValue(deliveryOrdersRef, (snapshot) => {
            const data = snapshot.val();
            if (data) {
                console.log('✅ Datos obtenidos de Firebase:', Object.keys(data).length, 'órdenes');
                setFirebaseData(data);
                setRealDeliveryOrders(data);
            }
        });
        
        // Escuchar cambios en el booking específico
        onValue(currentBookingRef, (snapshot) => {
            const bookingData = snapshot.val();
            if (bookingData && bookingData.delivery_orders) {
                // Actualizar paramData con los datos más recientes
                paramData.delivery_orders = bookingData.delivery_orders;
            }
        });
        
    } catch (error) {
        console.error('Error conectando a Firebase:', error);
    }
};
```

### 4. **Búsqueda de Productos en Firebase**
```javascript
const findProductsInFirebase = (orderId) => {
    // Primero buscar en los datos reales de Firebase
    if (realDeliveryOrders) {
        for (const key in realDeliveryOrders) {
            const order = realDeliveryOrders[key];
            if (order.orderId === orderId) {
                return order.products || [];
            }
        }
    }
    
    // Si no se encuentra, buscar en datos de respaldo
    if (firebaseData) {
        for (const key in firebaseData) {
            const order = firebaseData[key];
            if (order.orderId === orderId) {
                return order.products || [];
            }
        }
    }
    
    return [];
};
```

### 5. **Obtención de Órdenes con Prioridad Firebase**
```javascript
const getOrdersInfo = () => {
    // Primero intentar obtener datos reales de Firebase
    if (realDeliveryOrders) {
        const ordersArray = Object.values(realDeliveryOrders);
        console.log('✅ Usando datos reales de Firebase:', ordersArray.length, 'órdenes');
        
        return {
            totalOrders: ordersArray.length,
            totalProducts: ordersArray.reduce((total, order) => {
                return total + (order.products?.length || 0);
            }, 0),
            orders: ordersArray
        };
    }
    
    // Si no hay datos reales, usar datos locales de paramData
    if (paramData?.delivery_orders) {
        // ... lógica para datos locales
    }
    
    // Si no hay datos, crear ejemplos
    // ... datos de ejemplo
};
```

### 6. **Guardado Real en Firebase**
```javascript
const saveDeliveryToFirebase = async (orderId, productId, quantity, status) => {
    try {
        // Actualizar estado local inmediatamente
        const key = `${orderId}-${productId}`;
        setDeliveredQuantities(prev => ({ ...prev, [key]: quantity }));
        setDeliveryStatus(prev => ({ ...prev, [key]: status }));
        
        // Guardar en Firebase Real Database
        if (realDeliveryOrders) {
            for (const firebaseKey in realDeliveryOrders) {
                const order = realDeliveryOrders[firebaseKey];
                if (order.orderId === orderId) {
                    const productPath = `delivery_orders/${firebaseKey}/products`;
                    
                    // Actualizar el producto específico
                    const updatedProducts = order.products.map(product => {
                        if (product.id === productId) {
                            return {
                                ...product,
                                deliveredQuantity: quantity,
                                status: status,
                                lastUpdated: new Date().toISOString()
                            };
                        }
                        return product;
                    });
                    
                    // Guardar en Firebase
                    const productRef = ref(firebase.database, productPath);
                    await set(productRef, updatedProducts);
                    console.log('✅ Datos guardados en Firebase correctamente');
                    break;
                }
            }
        }
    } catch (error) {
        console.error('Error guardando en Firebase:', error);
    }
};
```

## Estructura de Datos en Firebase

### delivery_orders
```json
{
  "delivery_orders": {
    "-OX5wmxFoPAiUu-47SUz": {
      "orderId": "ORD-ME21XKJU-V6GY3E",
      "customerName": "Benito Ballesteros",
      "customerPhone": "",
      "customerEmail": "",
      "deliveryAddress": {
        "address": "Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco",
        "lat": 20.7094478,
        "lng": -103.4097295
      },
      "products": [
        {
          "id": "PROD-ME21XKJT-MRKR",
          "name": "Documentos Legales",
          "quantity": 2,
          "sku": "DOC-001",
          "description": "Contratos y documentos importantes",
          "weight": 0.5,
          "status": "PENDING",
          "deliveredQuantity": 0,
          "lastUpdated": "2025-01-08T..."
        }
      ]
    }
  }
}
```

### bookings
```json
{
  "bookings": {
    "booking_id": {
      "delivery_orders": [
        {
          "orderId": "ORD-ME21XKJU-V6GY3E",
          "customerName": "Benito Ballesteros",
          "deliveryAddress": "Av. Patria 1891...",
          "productCount": 3
        }
      ]
    }
  }
}
```

## Flujo de Datos

1. **Inicialización**: Se conecta a Firebase al cargar el componente
2. **Escucha en Tiempo Real**: Monitorea cambios en `delivery_orders` y `bookings`
3. **Prioridad de Datos**: 
   - Primero: Datos reales de Firebase
   - Segundo: Datos locales del booking
   - Tercero: Datos de ejemplo
4. **Actualización**: Los cambios se guardan inmediatamente en Firebase
5. **Sincronización**: Los datos se actualizan en tiempo real para todos los usuarios

## Beneficios

✅ **Datos en Tiempo Real**: Los cambios se reflejan inmediatamente
✅ **Persistencia**: Los datos se guardan permanentemente
✅ **Sincronización**: Múltiples usuarios ven los mismos datos
✅ **Respaldo**: Sistema de fallback con datos de ejemplo
✅ **Logging**: Registro detallado para debugging

## Próximos Pasos

1. **Optimización**: Implementar caché local para mejor rendimiento
2. **Validaciones**: Agregar validaciones de datos antes de guardar
3. **Manejo de Errores**: Mejorar el manejo de errores de red
4. **Offline Support**: Implementar soporte para modo offline
5. **Notificaciones**: Agregar notificaciones push para cambios importantes

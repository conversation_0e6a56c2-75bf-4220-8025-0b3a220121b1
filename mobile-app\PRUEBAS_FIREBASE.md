# Guía de Pruebas - Conexión Firebase Real

## Cómo Verificar que Funciona

### 1. **Verificar Logs en Consola**

Al abrir la pantalla RideDetails, deberías ver estos logs:

```
Conectando a Firebase Real Database...
Booking ID actual: [ID_DEL_BOOKING]
✅ Datos de delivery_orders obtenidos de Firebase: [NÚMERO] órdenes
✅ Datos del booking actual obtenidos: [NÚMERO] órdenes
✅ Usando datos reales de Firebase: [NÚMERO] órdenes
✅ Productos encontrados en Firebase para orden [ORDER_ID]: [NÚMERO]
```

### 2. **Verificar Datos en la Interfaz**

La pantalla ahora debería mostrar:

- **Órdenes reales** de la base de datos Firebase
- **Productos reales** con información completa:
  - Nombre del producto
  - Cantidad
  - SKU
  - Descripción
  - Peso
  - Estado

### 3. **Probar Funcionalidad de Entrega**

1. **Expandir un producto**: Tocar cualquier producto para ver detalles
2. **Cambiar cantidad**: Usar los botones + y - para modificar cantidad entregada
3. **Marcar como completo**: Usar el botón "Marcar como Entregado"
4. **Verificar guardado**: Deberías ver el log:
   ```
   ✅ Datos guardados en Firebase correctamente
   ```

### 4. **Verificar en Firebase Console**

1. Ir a [Firebase Console](https://console.firebase.google.com/)
2. Seleccionar proyecto "balle-813e3"
3. Ir a "Realtime Database"
4. Navegar a `delivery_orders > [ORDER_KEY] > products`
5. Verificar que los productos tienen:
   - `deliveredQuantity`: cantidad entregada
   - `status`: estado actualizado
   - `lastUpdated`: timestamp de última actualización

## Estructura de Datos Esperada

### En Firebase Console deberías ver:

```
delivery_orders/
├── -OX5wmxFoPAiUu-47SUz/
│   ├── orderId: "ORD-ME21XKJU-V6GY3E"
│   ├── customerName: "Benito Ballesteros"
│   ├── products/
│   │   └── 0/
│   │       ├── id: "PROD-ME21XKJT-MRKR"
│   │       ├── name: "Documentos Legales"
│   │       ├── quantity: 2
│   │       ├── deliveredQuantity: 0 (se actualiza)
│   │       ├── status: "PENDING" (se actualiza)
│   │       └── lastUpdated: "2025-01-08T..." (se actualiza)
```

## Casos de Prueba

### Caso 1: Datos Reales Disponibles
- **Condición**: Firebase tiene datos en `delivery_orders`
- **Resultado Esperado**: Se muestran datos reales de Firebase
- **Log**: "✅ Usando datos reales de Firebase: X órdenes"

### Caso 2: Solo Datos Locales
- **Condición**: Firebase no tiene datos, pero `paramData.delivery_orders` existe
- **Resultado Esperado**: Se muestran datos locales del booking
- **Log**: "📦 Usando delivery_orders locales: X elementos"

### Caso 3: Sin Datos
- **Condición**: No hay datos en Firebase ni locales
- **Resultado Esperado**: Se muestran datos de ejemplo
- **Log**: "⚠️ No se encontraron delivery_orders, creando órdenes de ejemplo"

### Caso 4: Error de Conexión
- **Condición**: Error al conectar con Firebase
- **Resultado Esperado**: Se usan datos de respaldo
- **Log**: "Error al obtener datos de Firebase: [ERROR]"

## Debugging

### Si no ves productos:

1. **Verificar conexión a internet**
2. **Verificar logs de consola** para errores
3. **Verificar que el booking tiene delivery_orders**
4. **Verificar permisos de Firebase**

### Si los cambios no se guardan:

1. **Verificar logs**: Debe aparecer "✅ Datos guardados en Firebase correctamente"
2. **Verificar permisos de escritura** en Firebase
3. **Verificar que `realDeliveryOrders` tiene datos**

### Comandos de Debug:

```javascript
// En la consola del navegador/debugger:
console.log('paramData:', paramData);
console.log('realDeliveryOrders:', realDeliveryOrders);
console.log('firebaseData:', firebaseData);
console.log('ordersInfo:', getOrdersInfo());
```

## Logs de Éxito

Cuando todo funciona correctamente, deberías ver:

```
Conectando a Firebase Real Database...
Booking ID actual: -OX5wmxXZI8tP9wW2oWt
✅ Datos de delivery_orders obtenidos de Firebase: 150 órdenes
✅ Datos del booking actual obtenidos: 3 órdenes
✅ Usando datos reales de Firebase: 150 órdenes
✅ Productos encontrados en Firebase para orden ORD-ME21XKJU-V6GY3E: 1
✅ Productos encontrados en Firebase para orden ORD-ME21XKJU-3GF3C: 1
✅ Productos encontrados en Firebase para orden ORD-ME21XKJU-PV1RE: 2
Guardando en Firebase: OrderId=ORD-ME21XKJU-V6GY3E, ProductId=PROD-ME21XKJT-MRKR, Quantity=1, Status=PARTIAL
✅ Datos guardados en Firebase correctamente
```

## Solución de Problemas Comunes

### Error: "firebase is not defined"
- **Causa**: Problema con la importación de Firebase
- **Solución**: Verificar que `import { firebase } from 'common';` está correcto

### Error: "Cannot read property 'database' of undefined"
- **Causa**: Firebase no está inicializado
- **Solución**: Verificar que FirebaseProvider está configurado en App.js

### No se muestran productos
- **Causa**: Datos no están en el formato esperado
- **Solución**: Verificar estructura de datos en Firebase Console

### Los cambios no se guardan
- **Causa**: Permisos de escritura o estructura incorrecta
- **Solución**: Verificar reglas de Firebase y estructura de datos

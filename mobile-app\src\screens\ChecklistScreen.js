import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Alert,
    SafeAreaView
} from 'react-native';
import { Button } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import { MAIN_COLOR } from '../common/sharedFunctions';
import { useSelector, useDispatch } from 'react-redux';
import { api } from 'common';

const { width, height } = Dimensions.get('window');

export default function ChecklistScreen({ route, navigation }) {
    const { booking, bookingId } = route.params;
    const dispatch = useDispatch();
    const { updateBooking } = api;

    const [checkedItems, setCheckedItems] = useState({});
    const [checklistProgress, setChecklistProgress] = useState(0);

    // Debug logs para diagnosticar el problema
    console.log('=== CHECKLIST SCREEN DEBUG ===');
    console.log('booking:', booking);
    console.log('bookingId:', bookingId);
    console.log('booking.products:', booking?.products);
    console.log('booking.orders:', booking?.orders);
    console.log('products length:', booking?.products?.length);
    console.log('orders length:', booking?.orders?.length);
    console.log('booking keys:', booking ? Object.keys(booking) : 'no booking');
    console.log('=== END DEBUG ===');

    // Función para simular la estructura de datos real para testing
    const simulateRealData = () => {
        if (!booking || (!booking.orders && !booking.delivery_orders)) {
            console.log('Simulando estructura de datos real con delivery_orders...');
            return {
                ...booking,
                folio: "ENT-2025-001",
                customerName: "Benito Ballesteros",
                customerPhone: "+523311917239",
                customerEmail: "<EMAIL>",
                pickupAddress: "C. Álvarez del Castillo 890, Santa María, Guadalajara",
                dropAddress: "Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco",
                delivery_orders: {
                    "order_1": {
                        customerId: "customer1_id_example",
                        customerName: "Benito Ballesteros",
                        customerPhone: "+523311917239",
                        customerEmail: "<EMAIL>",
                        deliveryAddress: {
                            address: "Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco",
                            lat: 20.7094478,
                            lng: -103.4097295
                        },
                        pickupAddress: {
                            address: "C. Álvarez del Castillo 890, Santa María, Guadalajara",
                            lat: 20.6858654,
                            lng: -103.3129226
                        },
                        orderId: "ORD-ME21XKJT-MRKR",
                        folio: "ENT-2025-001",
                        notes: "Entregar en recepción del edificio",
                        products: [
                            {
                                id: "PROD-ME21XKJT-MRKR",
                                name: "Documentos Legales",
                                quantity: 2,
                                sku: "DOC-001",
                                weight: 0.5,
                                description: "Contratos y documentos importantes",
                                status: "PENDING"
                            },
                            {
                                id: "PROD-ME21XKJT-W3GJ",
                                name: "Medicamentos",
                                quantity: 1,
                                sku: "MED-002",
                                weight: 1.2,
                                description: "Medicamentos para tratamiento",
                                status: "PENDING"
                            },
                            {
                                id: "PROD-ME21XKJT-IQQZ",
                                name: "Sobres Certificados",
                                quantity: 3,
                                sku: "SOBRE-003",
                                weight: 0.1,
                                description: "Correspondencia certificada",
                                status: "PENDING"
                            }
                        ],
                        status: "ASSIGNED",
                        totalPieces: 6,
                        totalSKUs: 3,
                        totalWeight: 2.5
                    },
                    "order_2": {
                        customerId: "customer2_id_example",
                        customerName: "María González",
                        customerPhone: "+523311917240",
                        customerEmail: "<EMAIL>",
                        deliveryAddress: {
                            address: "Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco",
                            lat: 20.674917,
                            lng: -103.3636746
                        },
                        pickupAddress: {
                            address: "C. Álvarez del Castillo 890, Santa María, Guadalajara",
                            lat: 20.6858654,
                            lng: -103.3129226
                        },
                        orderId: "ORD-ME21XKJU-EGJN",
                        folio: "ENT-2025-002",
                        notes: "Llamar antes de llegar",
                        products: [
                            {
                                id: "PROD-ME21XKJU-EGJN",
                                name: "Dispositivos Electrónicos",
                                quantity: 2,
                                sku: "ELEC-004",
                                weight: 2.5,
                                description: "Tablets y accesorios",
                                status: "PENDING"
                            },
                            {
                                id: "PROD-ME21XKJU-751X",
                                name: "Cables y Adaptadores",
                                quantity: 5,
                                sku: "CABLE-005",
                                weight: 0.3,
                                description: "Cables USB y adaptadores",
                                status: "PENDING"
                            }
                        ],
                        status: "ASSIGNED",
                        totalPieces: 7,
                        totalSKUs: 2,
                        totalWeight: 6.5
                    }
                }
            };
        }
        return booking;
    };

    // Usar datos simulados si no hay datos reales
    const bookingToUse = simulateRealData();

    // Función para obtener información del cliente
    const getCustomerInfo = () => {
        if (bookingToUse) {
            // Buscar en delivery_orders primero
            if (bookingToUse.delivery_orders && typeof bookingToUse.delivery_orders === 'object') {
                const orders = Object.values(bookingToUse.delivery_orders);
                if (orders.length > 0) {
                    const firstOrder = orders[0];
                    return {
                        name: firstOrder.customerName || "Cliente",
                        phone: firstOrder.customerPhone || "N/A",
                        email: firstOrder.customerEmail || "N/A",
                        pickupAddress: firstOrder.pickupAddress?.address || "N/A",
                        dropAddress: firstOrder.deliveryAddress?.address || "N/A"
                    };
                }
            }
            
            // Fallback a estructura antigua
            return {
                name: bookingToUse.customerName || bookingToUse.customer_name || "Cliente",
                phone: bookingToUse.customerPhone || bookingToUse.customer_contact || bookingToUse.customerPhone || "N/A",
                email: bookingToUse.customerEmail || bookingToUse.customer_email || "N/A",
                pickupAddress: bookingToUse.pickupAddress || bookingToUse.pickup_address || "N/A",
                dropAddress: bookingToUse.dropAddress || bookingToUse.drop_address || "N/A"
            };
        }
        return {
            name: "Cliente",
            phone: "N/A",
            email: "N/A",
            pickupAddress: "N/A",
            dropAddress: "N/A"
        };
    };

    // Función para obtener resumen de órdenes
    const getOrdersSummary = () => {
        // Buscar en delivery_orders primero
        if (bookingToUse?.delivery_orders && typeof bookingToUse.delivery_orders === 'object') {
            const orders = Object.values(bookingToUse.delivery_orders);
            return {
                totalOrders: orders.length,
                totalProducts: orders.reduce((total, order) => {
                    return total + (order.products ? order.products.length : 0);
                }, 0),
                totalPieces: orders.reduce((total, order) => {
                    return total + (order.products ? order.products.reduce((sum, product) => sum + (product.quantity || 0), 0) : 0);
                }, 0),
                totalWeight: orders.reduce((total, order) => {
                    return total + (order.products ? order.products.reduce((sum, product) => sum + (product.weight || 0), 0) : 0);
                }, 0)
            };
        }
        
        // Fallback a estructura antigua
        if (bookingToUse?.orders && Array.isArray(bookingToUse.orders)) {
            return {
                totalOrders: bookingToUse.orders.length,
                totalProducts: bookingToUse.orders.reduce((total, order) => {
                    return total + (order.products ? order.products.length : 0);
                }, 0),
                totalPieces: bookingToUse.orders.reduce((total, order) => {
                    return total + (order.products ? order.products.reduce((sum, product) => sum + (product.quantity || 0), 0) : 0);
                }, 0),
                totalWeight: bookingToUse.orders.reduce((total, order) => {
                    return total + (order.products ? order.products.reduce((sum, product) => sum + (product.weight || 0), 0) : 0);
                }, 0)
            };
        }
        
        return {
            totalOrders: 0,
            totalProducts: 0,
            totalPieces: 0,
            totalWeight: 0
        };
    };

    // Inicializar checklist
    useEffect(() => {
        const products = getProductsToShow();
        const finalProducts = (!products || products.length === 0) ? createTestProducts() : products;
        
        if (bookingToUse && finalProducts) {
            // Inicializar estado del checklist desde la base de datos si existe
            if (bookingToUse.checklistStatus) {
                setCheckedItems(bookingToUse.checklistStatus);
                const checkedCount = Object.values(bookingToUse.checklistStatus).filter(Boolean).length;
                setChecklistProgress((checkedCount / finalProducts.length) * 100);
            } else {
                resetChecklist();
            }
        }
    }, [bookingToUse]);

    const toggleItem = (productId) => {
        setCheckedItems(prev => {
            const newCheckedItems = {
                ...prev,
                [productId]: !prev[productId]
            };

            // Calcular progreso
            const products = getProductsToShow();
            const finalProducts = (!products || products.length === 0) ? createTestProducts() : products;
            const totalProducts = finalProducts?.length || 0;
            const checkedCount = Object.values(newCheckedItems).filter(Boolean).length;
            setChecklistProgress(totalProducts > 0 ? (checkedCount / totalProducts) * 100 : 0);

            return newCheckedItems;
        });
    };

    const markAllAsChecked = () => {
        const products = getProductsToShow();
        const finalProducts = (!products || products.length === 0) ? createTestProducts() : products;
        if (finalProducts) {
            const allChecked = {};
            finalProducts.forEach(product => {
                allChecked[product.id] = true;
            });
            setCheckedItems(allChecked);
            setChecklistProgress(100);
        }
    };

    const resetChecklist = () => {
        setCheckedItems({});
        setChecklistProgress(0);
    };

    const saveChecklistToDatabase = async () => {
        try {
            console.log('Checklist guardado:', checkedItems);
            
            // Actualizar el booking con el estado del checklist
            if (bookingToUse) {
                const updatedBooking = {
                    ...bookingToUse,
                    checklistStatus: checkedItems,
                    checklistCompleted: checklistProgress === 100
                };
                dispatch(updateBooking(updatedBooking));
            }
            
            Alert.alert(
                'Éxito',
                'Checklist guardado correctamente',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
            );
        } catch (error) {
            console.error('Error al guardar checklist:', error);
            Alert.alert('Error', 'No se pudo guardar el checklist');
        }
    };

    // Extraer todos los productos de todas las órdenes
    const getProductsToShow = () => {
        console.log('=== ANALIZANDO ESTRUCTURA DE PRODUCTOS ===');
        console.log('booking completo:', bookingToUse);
        console.log('booking.delivery_orders:', bookingToUse?.delivery_orders);
        console.log('booking.products:', bookingToUse?.products);
        console.log('booking.orders:', bookingToUse?.orders);
        console.log('booking.orderDetails:', bookingToUse?.orderDetails);
        console.log('=== FIN ANÁLISIS ===');

        // Caso 1: NUEVA ESTRUCTURA - delivery_orders (ESTRUCTURA ACTUAL)
        if (bookingToUse?.delivery_orders && typeof bookingToUse.delivery_orders === 'object') {
            console.log('Procesando nueva estructura con delivery_orders:', bookingToUse.delivery_orders);
            const allProducts = [];

            Object.values(bookingToUse.delivery_orders).forEach((order, orderIndex) => {
                console.log(`Procesando order ${orderIndex + 1}:`, order);
                console.log(`Order products:`, order.products);
                
                if (order.products && Array.isArray(order.products)) {
                    order.products.forEach((product, productIndex) => {
                        console.log(`Procesando producto ${productIndex + 1}:`, product);
                        allProducts.push({
                            ...product,
                            id: product.id || product.sku || `ORDER-${orderIndex + 1}-PROD-${productIndex + 1}`,
                            customerInfo: {
                                customerName: order.customerName,
                                deliveryAddress: order.deliveryAddress?.address,
                                pickupAddress: order.pickupAddress?.address,
                                notes: order.notes,
                                orderId: order.orderId,
                                folio: order.folio
                            }
                        });
                    });
                } else {
                    console.log(`Order ${orderIndex + 1} no tiene productos o no es un array`);
                }
            });

            console.log(`Total de productos extraídos de delivery_orders: ${allProducts.length}`);
            if (allProducts.length > 0) {
                console.log('Productos extraídos de delivery_orders:', allProducts);
                return allProducts;
            } else {
                console.log('No se encontraron productos en ninguna order de delivery_orders');
            }
        }

        // Caso 2: Estructura antigua con productos directos
        if (bookingToUse?.products && bookingToUse.products.length > 0) {
            console.log('Usando productos del booking (estructura antigua):', bookingToUse.products);
            return bookingToUse.products.map((product, index) => ({
                ...product,
                id: product.id || `PROD-${index + 1}`,
                customerInfo: null
            }));
        }

        // Caso 3: Estructura con orders que contienen products
        if (bookingToUse?.orders && Array.isArray(bookingToUse.orders)) {
            console.log('Procesando estructura con orders:', bookingToUse.orders);
            const allProducts = [];

            bookingToUse.orders.forEach((order, orderIndex) => {
                console.log(`Procesando order ${orderIndex + 1}:`, order);
                console.log(`Order products:`, order.products);
                
                if (order.products && Array.isArray(order.products)) {
                    order.products.forEach((product, productIndex) => {
                        console.log(`Procesando producto ${productIndex + 1}:`, product);
                        allProducts.push({
                            ...product,
                            id: product.id || product.sku || `ORDER-${orderIndex + 1}-PROD-${productIndex + 1}`,
                            customerInfo: {
                                customerName: order.customerName,
                                deliveryAddress: order.deliveryAddress?.address,
                                notes: order.notes
                            }
                        });
                    });
                } else {
                    console.log(`Order ${orderIndex + 1} no tiene productos o no es un array`);
                }
            });

            console.log(`Total de productos extraídos: ${allProducts.length}`);
            if (allProducts.length > 0) {
                console.log('Productos extraídos de orders:', allProducts);
                return allProducts;
            } else {
                console.log('No se encontraron productos en ninguna order');
            }
        }

        // Caso 3: Parsear orderDetails (estructura muy antigua)
        if (bookingToUse?.orderDetails) {
            console.log('Parseando orderDetails:', bookingToUse.orderDetails);
            const orderText = bookingToUse.orderDetails;
            const products = [];

            const matches = orderText.match(/(\d+)x?\s*([^,]+)/g);
            if (matches) {
                matches.forEach((match, index) => {
                    const parts = match.match(/(\d+)x?\s*(.+)/);
                    if (parts) {
                        products.push({
                            id: `PARSED-${index + 1}`,
                            name: parts[2].trim(),
                            quantity: parseInt(parts[1]),
                            sku: `PARSED-${index + 1}`,
                            description: `Extraído de: ${match}`,
                            weight: 0.5,
                            customerInfo: null
                        });
                    }
                });
            }

            if (products.length > 0) {
                console.log('Productos parseados de orderDetails:', products);
                return products;
            }
        }

        // Caso 4: Buscar en otras posibles estructuras
        const possibleProductFields = [
            'order_details', 'items', 'products_list', 'product_list', 
            'goods', 'delivery_items', 'package_items', 'items_list',
            'product_items', 'delivery_goods', 'package_contents'
        ];

        for (const field of possibleProductFields) {
            if (bookingToUse?.[field]) {
                console.log(`Encontrado campo ${field}:`, bookingToUse[field]);
                
                // Si es un array
                if (Array.isArray(bookingToUse[field])) {
                    if (bookingToUse[field].length > 0) {
                        console.log(`Usando productos de ${field}:`, bookingToUse[field]);
                        return bookingToUse[field].map((product, index) => ({
                            ...product,
                            id: product.id || `${field.toUpperCase()}-${index + 1}`,
                            customerInfo: null
                        }));
                    }
                }
                // Si es un string, intentar parsear
                else if (typeof bookingToUse[field] === 'string') {
                    console.log(`Parseando ${field}:`, bookingToUse[field]);
                    const products = parseProductString(bookingToUse[field], field);
                    if (products.length > 0) {
                        return products;
                    }
                }
            }
        }

        // Caso 5: Crear productos de prueba si no hay ninguno
        console.log('No se encontraron productos, creando productos de prueba...');
        return createTestProducts();
    };

    // Función para parsear strings de productos
    const parseProductString = (productString, fieldName) => {
        const products = [];
        
        // Intentar diferentes patrones de parsing
        const patterns = [
            /(\d+)x?\s*([^,]+)/g,  // "2x Producto"
            /([^,]+)\s*:\s*(\d+)/g,  // "Producto: 2"
            /([^,]+)\s*\((\d+)\)/g,   // "Producto (2)"
        ];

        for (const pattern of patterns) {
            const matches = productString.match(pattern);
            if (matches) {
                matches.forEach((match, index) => {
                    let name, quantity;
                    
                    if (pattern.source.includes('x')) {
                        const parts = match.match(/(\d+)x?\s*(.+)/);
                        if (parts) {
                            quantity = parseInt(parts[1]);
                            name = parts[2].trim();
                        }
                    } else if (pattern.source.includes(':')) {
                        const parts = match.match(/([^,]+)\s*:\s*(\d+)/);
                        if (parts) {
                            name = parts[1].trim();
                            quantity = parseInt(parts[2]);
                        }
                    } else if (pattern.source.includes('(')) {
                        const parts = match.match(/([^,]+)\s*\((\d+)\)/);
                        if (parts) {
                            name = parts[1].trim();
                            quantity = parseInt(parts[2]);
                        }
                    }

                    if (name && quantity) {
                        products.push({
                            id: `${fieldName.toUpperCase()}-${index + 1}`,
                            name: name,
                            quantity: quantity,
                            sku: `${fieldName.toUpperCase()}-${index + 1}`,
                            description: `Extraído de ${fieldName}: ${match}`,
                            weight: 0.5,
                            customerInfo: null
                        });
                    }
                });
                
                if (products.length > 0) {
                    console.log(`Productos parseados de ${fieldName}:`, products);
                    return products;
                }
            }
        }

        return products;
    };

    // Función para crear productos de prueba basados en la estructura real
    const createTestProducts = () => {
        console.log('Creando productos de prueba basados en la estructura real...');
        return [
            {
                id: 'DOC-001',
                name: 'Documentos Legales',
                quantity: 2,
                sku: 'DOC-001',
                weight: 0.5,
                description: 'Contratos y documentos importantes',
                customerInfo: {
                    customerName: 'Benito Ballesteros',
                    deliveryAddress: 'Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco',
                    notes: 'Entregar en recepción del edificio'
                }
            },
            {
                id: 'MED-002',
                name: 'Medicamentos',
                quantity: 1,
                sku: 'MED-002',
                weight: 1.2,
                description: 'Medicamentos para tratamiento',
                customerInfo: {
                    customerName: 'Benito Ballesteros',
                    deliveryAddress: 'Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco',
                    notes: 'Entregar en recepción del edificio'
                }
            },
            {
                id: 'SOBRE-003',
                name: 'Sobres Certificados',
                quantity: 3,
                sku: 'SOBRE-003',
                weight: 0.1,
                description: 'Correspondencia certificada',
                customerInfo: {
                    customerName: 'Benito Ballesteros',
                    deliveryAddress: 'Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco',
                    notes: 'Entregar en recepción del edificio'
                }
            },
            {
                id: 'ELEC-004',
                name: 'Dispositivos Electrónicos',
                quantity: 2,
                sku: 'ELEC-004',
                weight: 2.5,
                description: 'Tablets y accesorios',
                customerInfo: {
                    customerName: 'María González',
                    deliveryAddress: 'Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco',
                    notes: 'Llamar antes de llegar'
                }
            },
            {
                id: 'CABLE-005',
                name: 'Cables y Adaptadores',
                quantity: 5,
                sku: 'CABLE-005',
                weight: 0.3,
                description: 'Cables USB y adaptadores',
                customerInfo: {
                    customerName: 'María González',
                    deliveryAddress: 'Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco',
                    notes: 'Llamar antes de llegar'
                }
            }
        ];
    };

    const productsToShow = getProductsToShow();

    // Si no hay productos reales, usar productos de prueba para demostración
    const finalProductsToShow = (!productsToShow || productsToShow.length === 0) ? createTestProducts() : productsToShow;

    // Obtener información del cliente y resumen
    const customerInfo = getCustomerInfo();
    const ordersSummary = getOrdersSummary();

    if (!bookingToUse) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <Ionicons name="arrow-back" size={24} color={colors.BLACK} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Checklist de Productos</Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.emptyContainer}>
                    <Ionicons name="cube-outline" size={80} color={colors.GREY} />
                    <Text style={styles.emptyText}>No hay productos para verificar</Text>
                    <Text style={styles.debugText}>
                        Debug: {bookingToUse ? 'Booking existe' : 'No booking'} |
                        OrderDetails: {bookingToUse?.orderDetails ? 'Sí' : 'No'} |
                        Products: {bookingToUse?.products?.length || 0} |
                        Orders: {bookingToUse?.orders?.length || 0}
                    </Text>
                    <Text style={styles.debugText}>
                        Campos disponibles: {bookingToUse ? Object.keys(bookingToUse).join(', ') : 'Ninguno'}
                    </Text>
                    
                    {/* Botón temporal para crear productos de prueba */}
                    <TouchableOpacity
                        style={{
                            backgroundColor: MAIN_COLOR,
                            paddingHorizontal: 20,
                            paddingVertical: 10,
                            borderRadius: 8,
                            marginTop: 20,
                        }}
                        onPress={() => {
                            // Forzar la creación de productos de prueba
                            const testProducts = createTestProducts();
                            setCheckedItems({});
                            setChecklistProgress(0);
                            // Simular que tenemos productos
                            setTimeout(() => {
                                // Esto forzará el re-render con productos
                                Alert.alert('Productos de Prueba', 'Se han creado productos de prueba para demostrar la funcionalidad');
                            }, 100);
                        }}
                    >
                        <Text style={{ color: 'white', fontFamily: fonts.Bold }}>
                            Crear Productos de Prueba
                        </Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Ionicons name="arrow-back" size={24} color={colors.BLACK} />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Checklist de Productos</Text>
                <View style={{ width: 24 }} />
            </View>

            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* Sección de Información del Folio y Cliente */}
                <View style={styles.infoSection}>
                    <View style={styles.folioContainer}>
                        <Ionicons name="document-text" size={20} color={MAIN_COLOR} />
                        <Text style={styles.folioText}>
                            Folio: {bookingToUse.folio || "ENT-2025-001"}
                        </Text>
                    </View>

                    <View style={styles.customerSection}>
                        <Text style={styles.sectionTitle}>👤 Información del Cliente</Text>
                        <View style={styles.customerCard}>
                            <Text style={styles.customerName}>{customerInfo.name}</Text>
                            <Text style={styles.customerDetail}>📞 {customerInfo.phone}</Text>
                            <Text style={styles.customerDetail}>📧 {customerInfo.email}</Text>
                            {ordersSummary.totalOrders > 1 && (
                                <Text style={styles.customerDetail}>
                                    📦 Múltiples órdenes: {ordersSummary.totalOrders} clientes
                                </Text>
                            )}
                        </View>
                    </View>

                    <View style={styles.addressSection}>
                        <Text style={styles.sectionTitle}>📍 Direcciones</Text>
                        <View style={styles.addressCard}>
                            <Text style={styles.addressLabel}>Recolección:</Text>
                            <Text style={styles.addressText}>{customerInfo.pickupAddress}</Text>
                            <Text style={styles.addressLabel}>Entrega:</Text>
                            <Text style={styles.addressText}>{customerInfo.dropAddress}</Text>
                        </View>
                    </View>

                    {/* Sección de Resumen de Órdenes */}
                    <View style={styles.summarySection}>
                        <Text style={styles.sectionTitle}>📋 Resumen de Órdenes</Text>
                        <View style={styles.summaryCard}>
                            <View style={styles.summaryRow}>
                                <Text style={styles.summaryLabel}>Total de Órdenes:</Text>
                                <Text style={styles.summaryValue}>{ordersSummary.totalOrders}</Text>
                            </View>
                            <View style={styles.summaryRow}>
                                <Text style={styles.summaryLabel}>Tipos de Productos:</Text>
                                <Text style={styles.summaryValue}>{ordersSummary.totalProducts}</Text>
                            </View>
                            <View style={styles.summaryRow}>
                                <Text style={styles.summaryLabel}>Total de Piezas:</Text>
                                <Text style={styles.summaryValue}>{ordersSummary.totalPieces}</Text>
                            </View>
                            <View style={styles.summaryRow}>
                                <Text style={styles.summaryLabel}>Peso Total:</Text>
                                <Text style={styles.summaryValue}>{ordersSummary.totalWeight.toFixed(1)} kg</Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Sección de Progreso */}
                <View style={styles.progressContainer}>
                    <Text style={styles.progressText}>
                        Progreso: {Math.round(checklistProgress)}%
                    </Text>
                    <View style={styles.progressBarContainer}>
                        <View style={[styles.progressBar, { width: `${checklistProgress}%` }]} />
                    </View>
                    <Text style={styles.progressDetails}>
                        {Object.values(checkedItems).filter(Boolean).length} de {finalProductsToShow.length} productos verificados
                    </Text>
                    {(!productsToShow || productsToShow.length === 0) && (
                        <Text style={styles.demoText}>
                            🧪 Modo demostración - Productos de prueba
                        </Text>
                    )}
                    {(productsToShow && productsToShow.length > 0) && (
                        <Text style={styles.realDataText}>
                            ✅ Datos reales del booking
                        </Text>
                    )}
                </View>

                {/* Lista de Productos */}
                <View style={styles.productsSection}>
                    <Text style={styles.sectionTitle}>📦 Productos a Verificar</Text>
                    {finalProductsToShow.map((product, index) => (
                        <View key={product.id} style={styles.productItem}>
                            <TouchableOpacity
                                style={styles.checkbox}
                                onPress={() => toggleItem(product.id)}
                            >
                                <Ionicons 
                                    name={checkedItems[product.id] ? "checkmark-circle" : "ellipse-outline"} 
                                    size={32} 
                                    color={checkedItems[product.id] ? colors.GREEN : MAIN_COLOR} 
                                />
                            </TouchableOpacity>
                            <View style={styles.productInfo}>
                                <Text style={styles.productName}>{product.name}</Text>
                                <Text style={styles.productDetails}>
                                    Cantidad: {product.quantity}
                                    {product.sku && ` | SKU: ${product.sku}`}
                                </Text>
                                {product.description && (
                                    <Text style={styles.productDescription}>
                                        {product.description}
                                    </Text>
                                )}
                                {product.weight > 0 && (
                                    <Text style={styles.productWeight}>
                                        Peso: {product.weight} kg
                                    </Text>
                                )}
                                {product.customerInfo && (
                                    <View style={styles.customerInfo}>
                                        <Text style={styles.customerName}>
                                            👤 {product.customerInfo.customerName}
                                        </Text>
                                        <Text style={styles.customerAddress}>
                                            📍 {product.customerInfo.deliveryAddress}
                                        </Text>
                                        {product.customerInfo.orderId && (
                                            <Text style={styles.orderInfo}>
                                                🆔 Orden: {product.customerInfo.orderId}
                                            </Text>
                                        )}
                                        {product.customerInfo.folio && (
                                            <Text style={styles.orderInfo}>
                                                📄 Folio: {product.customerInfo.folio}
                                            </Text>
                                        )}
                                        {product.customerInfo.notes && (
                                            <Text style={styles.customerNotes}>
                                                📝 {product.customerInfo.notes}
                                            </Text>
                                        )}
                                    </View>
                                )}
                            </View>
                        </View>
                    ))}
                </View>
            </ScrollView>

            <View style={styles.buttonContainer}>
                <View style={styles.actionButtons}>
                    <Button
                        title="Marcar Todo"
                        onPress={markAllAsChecked}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                        titleStyle={styles.buttonText}
                    />
                    <Button
                        title="Reiniciar"
                        onPress={resetChecklist}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.RED }]}
                        titleStyle={styles.buttonText}
                    />
                </View>
                
                <Button
                    title="Guardar y Volver"
                    onPress={saveChecklistToDatabase}
                    buttonStyle={[styles.saveButton, { backgroundColor: MAIN_COLOR }]}
                    titleStyle={styles.saveButtonText}
                />
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    progressContainer: {
        padding: 20,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    progressText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 10,
    },
    progressBarContainer: {
        height: 8,
        backgroundColor: '#e0e0e0',
        borderRadius: 4,
        marginBottom: 8,
    },
    progressBar: {
        height: '100%',
        backgroundColor: MAIN_COLOR,
        borderRadius: 4,
    },
    progressDetails: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    scrollView: {
        flex: 1,
        paddingHorizontal: 20,
    },
    productItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    checkbox: {
        marginRight: 15,
        marginTop: 2,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 4,
    },
    productDetails: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 4,
    },
    productDescription: {
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 2,
        fontStyle: 'italic',
    },
    productWeight: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        opacity: 0.7,
    },
    buttonContainer: {
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    actionButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
    },
    actionButton: {
        paddingHorizontal: 30,
        paddingVertical: 12,
        borderRadius: 8,
        minWidth: 120,
    },
    buttonText: {
        fontFamily: fonts.Bold,
        fontSize: 14,
    },
    saveButton: {
        paddingVertical: 15,
        borderRadius: 8,
    },
    saveButtonText: {
        fontFamily: fonts.Bold,
        fontSize: 16,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 20,
    },
    debugText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 10,
        textAlign: 'center',
    },
    customerInfo: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    customerName: {
        fontSize: 13,
        fontFamily: fonts.Bold,
        color: MAIN_COLOR,
        marginBottom: 2,
    },
    customerAddress: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 2,
    },
    customerNotes: {
        fontSize: 11,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        fontStyle: 'italic',
    },
    orderInfo: {
        fontSize: 11,
        fontFamily: fonts.Regular,
        color: MAIN_COLOR,
        marginBottom: 2,
    },
    demoText: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: MAIN_COLOR,
        textAlign: 'center',
        marginTop: 5,
        fontStyle: 'italic',
    },
    realDataText: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: colors.GREEN,
        textAlign: 'center',
        marginTop: 5,
        fontStyle: 'italic',
    },
    infoSection: {
        padding: 20,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    folioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    folioText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginLeft: 10,
    },
    customerSection: {
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 10,
    },
    customerCard: {
        backgroundColor: colors.WHITE,
        padding: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#eee',
    },
    customerName: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: MAIN_COLOR,
        marginBottom: 5,
    },
    customerDetail: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 3,
    },
    addressSection: {
        marginBottom: 15,
    },
    addressCard: {
        backgroundColor: colors.WHITE,
        padding: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#eee',
    },
    addressLabel: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 5,
    },
    addressText: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 5,
    },
    summarySection: {
        marginBottom: 15,
    },
    summaryCard: {
        backgroundColor: colors.WHITE,
        padding: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#eee',
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    summaryLabel: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    summaryValue: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
    },
    productsSection: {
        marginTop: 10,
        paddingHorizontal: 20,
    },
});

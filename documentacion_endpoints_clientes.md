# Documentación Técnica - Endpoints de Clientes

## Base URL
```
https://us-central1-transporte-vp.cloudfunctions.net
```

## Endpoints

### 1. <PERSON><PERSON>r Cliente
Crea un nuevo cliente en el sistema.

**URL**: `/createCustomer`  
**Método**: `POST`  
**Headers**:
```
Content-Type: application/json
```

**Body**:
```json
{
    "nombreComercial": "Nombre de la Empresa",
    "rfc": "RFC123456ABC",
    "mobile": "+523311234567",
    "email": "<EMAIL>",
    "firstName": "Nombre",         // Opcional
    "lastName": "Apellido",        // Opcional
    "address": "Dirección",        // Opcional
    "profile_image": "URL"         // Opcional
}
```

**Respuesta Exitosa**:
- Código: 200
```json
{
    "success": true,
    "message": "Cliente creado exitosamente",
    "data": {
        "id": "id-generado",
        "nombreComercial": "Nombre de la Empresa",
        "rfc": "RFC123456ABC",
        "mobile": "+523311234567",
        "email": "<EMAIL>",
        "usertype": "customer",
        "approved": true,
        "createdAt": 1234567890,
        "referralId": "XXXXX",
        "walletBalance": 0,
        "firstName": "Nombre",
        "lastName": "Apellido",
        "address": "Dirección",
        "profile_image": "URL"
    }
}
```

**Respuesta de Error**:
- Código: 400
```json
{
    "success": false,
    "error": "Mensaje de error específico"
}
```

### 2. Actualizar Cliente
Actualiza la información de un cliente existente.

**URL**: `/updateCustomer`  
**Método**: `POST`  
**Headers**:
```
Content-Type: application/json
```

**Body**:
```json
{
    "id": "id-del-cliente",
    "nombreComercial": "Nuevo Nombre Comercial",
    "rfc": "NUEVO123456ABC",
    "mobile": "+523311234567",
    "email": "<EMAIL>",
    "firstName": "Nuevo Nombre",    // Opcional
    "lastName": "Nuevo Apellido",   // Opcional
    "address": "Nueva Dirección",   // Opcional
    "profile_image": "URL"          // Opcional
}
```

**Respuesta Exitosa**:
- Código: 200
```json
{
    "success": true,
    "message": "Cliente actualizado exitosamente",
    "data": {
        "id": "id-del-cliente",
        "nombreComercial": "Nuevo Nombre Comercial",
        "rfc": "NUEVO123456ABC",
        "mobile": "+523311234567",
        "email": "<EMAIL>",
        "firstName": "Nuevo Nombre",
        "lastName": "Nuevo Apellido",
        "address": "Nueva Dirección",
        "profile_image": "URL",
        "updatedAt": 1234567890
    }
}
```

**Respuesta de Error**:
- Código: 400
```json
{
    "success": false,
    "error": "Mensaje de error específico"
}
```

## Validaciones

### Campos Requeridos
- `nombreComercial`: Nombre comercial de la empresa
- `rfc`: RFC de la empresa (texto)
- `mobile`: Número de teléfono móvil
- `email`: Correo electrónico

### Validaciones Específicas
1. **Email Único**: No puede existir otro usuario con el mismo email
2. **Teléfono Único**: No puede existir otro usuario con el mismo número de teléfono
3. **ID en Actualización**: Para actualizar, el ID del cliente debe existir y corresponder a un usuario tipo 'customer'

## Mensajes de Error Comunes

1. **Campos Faltantes**:
```json
{
    "success": false,
    "error": "Campos requeridos faltantes: [campos]"
}
```

2. **Email Duplicado**:
```json
{
    "success": false,
    "error": "Ya existe un usuario con este email"
}
```

3. **Teléfono Duplicado**:
```json
{
    "success": false,
    "error": "Ya existe un usuario con este número de teléfono"
}
```

4. **Cliente No Encontrado**:
```json
{
    "success": false,
    "error": "Cliente no encontrado"
}
```

5. **ID Incorrecto**:
```json
{
    "success": false,
    "error": "El ID proporcionado no corresponde a un cliente"
}
```

## Notas Adicionales

1. Todos los clientes se crean con:
   - `usertype`: "customer"
   - `approved`: true
   - `walletBalance`: 0
   - `term`: true

2. Se genera automáticamente:
   - `createdAt`: Timestamp de creación
   - `updatedAt`: Timestamp de actualización
   - `referralId`: ID de referencia aleatorio

3. Los campos opcionales (`firstName`, `lastName`, `address`, `profile_image`) se guardarán como cadenas vacías si no se proporcionan.

4. La respuesta siempre incluirá todos los campos del cliente, incluso aquellos que no se modificaron en la actualización. 
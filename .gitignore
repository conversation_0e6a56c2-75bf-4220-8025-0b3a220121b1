# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log*

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Expo
.expo/
web-build/
dist/

# Local Netlify folder
.netlify

# EAS Build
.eas-build-cache/
.eas-cache/

# Large files and directories
node_modules/.cache/
web-app/node_modules/.cache/
**/.cache/
**/build/
web-app/build/

# Environment variables
.env
.env.*
!.env.example
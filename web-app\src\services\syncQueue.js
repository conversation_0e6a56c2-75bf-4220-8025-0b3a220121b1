import sugarERPApi from './sugarERP';

class SyncQueue {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
    this.maxRetries = 3;
  }

  // Añadir item a la cola
  async addToQueue(type, data, priority = 1) {
    const queueItem = {
      id: Date.now(),
      type,
      data,
      priority,
      retries: 0,
      status: 'pending'
    };

    this.queue.push(queueItem);
    this.queue.sort((a, b) => b.priority - a.priority);

    if (!this.isProcessing) {
      this.processQueue();
    }

    return queueItem.id;
  }

  // Procesar la cola
  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const item = this.queue[0];

      try {
        await this.processItem(item);
        this.queue.shift(); // Remover item procesado
        this.saveQueueState(); // Guardar estado de la cola
      } catch (error) {
        console.error(`Error procesando item ${item.id}:`, error);
        
        if (item.retries < this.maxRetries) {
          item.retries++;
          item.status = 'retrying';
          // Mover al final de la cola de su prioridad
          this.queue.shift();
          this.queue.push(item);
        } else {
          item.status = 'failed';
          this.queue.shift();
          this.handleFailedSync(item);
        }
      }
    }

    this.isProcessing = false;
  }

  // Procesar un item específico
  async processItem(item) {
    switch (item.type) {
      case 'booking':
        await sugarERPApi.bookings.update(item.data);
        break;
      case 'user':
        await sugarERPApi.users.sync(item.data);
        break;
      case 'driver':
        await sugarERPApi.drivers.sync(item.data);
        break;
      default:
        throw new Error(`Tipo de sincronización no soportado: ${item.type}`);
    }
  }

  // Manejar sincronizaciones fallidas
  async handleFailedSync(item) {
    // Guardar en IndexedDB para reintento posterior
    try {
      const db = await this.openDatabase();
      const transaction = db.transaction(['failedSyncs'], 'readwrite');
      const store = transaction.objectStore('failedSyncs');
      await store.add({
        ...item,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error guardando sync fallido:', error);
    }
  }

  // Abrir conexión con IndexedDB
  async openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('syncQueueDB', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('failedSyncs')) {
          db.createObjectStore('failedSyncs', { keyPath: 'id' });
        }
      };
    });
  }

  // Guardar estado de la cola
  saveQueueState() {
    localStorage.setItem('syncQueue', JSON.stringify(this.queue));
  }

  // Cargar estado de la cola
  loadQueueState() {
    const savedQueue = localStorage.getItem('syncQueue');
    if (savedQueue) {
      this.queue = JSON.parse(savedQueue);
      this.processQueue();
    }
  }

  // Reintentar sincronizaciones fallidas
  async retryFailedSyncs() {
    try {
      const db = await this.openDatabase();
      const transaction = db.transaction(['failedSyncs'], 'readwrite');
      const store = transaction.objectStore('failedSyncs');
      const failedSyncs = await store.getAll();

      for (const sync of failedSyncs) {
        await this.addToQueue(sync.type, sync.data, 2); // Prioridad alta para reintento
        await store.delete(sync.id);
      }
    } catch (error) {
      console.error('Error retrying failed syncs:', error);
    }
  }
}

const syncQueue = new SyncQueue();
export default syncQueue; 